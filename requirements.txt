# Core Django Framework
Django>=4.2.0,<5.0
djangorestframework>=3.14.0

# Database
psycopg2-binary>=2.9.0  # PostgreSQL adapter
django-environ>=0.10.0  # Environment variables

# Authentication & Security
django-allauth>=0.54.0
django-cors-headers>=4.0.0
django-crispy-forms>=2.0
crispy-tailwind>=0.5.0

# File Handling & Media
Pillow>=9.5.0
django-storages>=1.13.0
boto3>=1.26.0  # For AWS S3 storage

# Email
django-ses>=3.4.0

# Forms & UI
django-widget-tweaks>=1.4.12
django-htmx>=1.14.0

# Development & Debugging
django-debug-toolbar>=4.0.0
django-extensions>=3.2.0

# Production & Performance
gunicorn>=20.1.0
whitenoise>=6.4.0
django-redis>=5.2.0

# Utilities
python-decouple>=3.8
requests>=2.28.0
celery>=5.2.0
redis>=4.5.0

# Testing
coverage>=7.2.0
factory-boy>=3.2.0
pytest-django>=4.5.0

# Tailwind CSS Integration
django-tailwind>=3.5.0
django-browser-reload>=1.7.0

# API & Serialization
djangorestframework-simplejwt>=5.2.0
django-filter>=23.1

# Monitoring & Logging
sentry-sdk>=1.20.0

# Date & Time
python-dateutil>=2.8.0

# Validation
django-phonenumber-field>=7.0.0
phonenumbers>=8.13.0

# Payment Integration (M-Pesa)
python-mpesa>=1.2.0

# Excel/CSV Export
openpyxl>=3.1.0
django-import-export>=3.2.0

# Background Tasks
django-rq>=2.7.0
rq>=1.13.0

# Caching
django-cachalot>=2.5.0

# Search
django-haystack>=3.2.0
whoosh>=2.7.0

# Social Authentication
social-auth-app-django>=5.2.0

# Admin Interface Enhancements
django-admin-interface>=0.19.0
django-colorfield>=0.8.0

# Internationalization
django-modeltranslation>=0.18.0

# API Documentation
drf-spectacular>=0.26.0

# Development Tools
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0
pre-commit>=3.2.0

# Enhanced Server Dependencies
psutil>=5.9.0  # System monitoring and resource tracking
cryptography>=3.4.8  # SSL/TLS support and security
schedule>=1.2.0  # Task scheduling for background workers
setuptools>=65.0.0  # Package management utilities
