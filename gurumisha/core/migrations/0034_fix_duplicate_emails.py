# Generated by Django 5.2 on 2025-07-16 08:25

from django.db import migrations
from django.db.models import Count


def fix_duplicate_emails(apps, schema_editor):
    """
    Fix duplicate email addresses by appending a suffix to duplicates.
    Keep the oldest account with the original email.
    """
    User = apps.get_model('core', 'User')

    # Find emails that have duplicates
    duplicate_emails = (
        User.objects.values('email')
        .annotate(count=Count('email'))
        .filter(count__gt=1)
        .values_list('email', flat=True)
    )

    for email in duplicate_emails:
        # Get all users with this email, ordered by date_joined (oldest first)
        users_with_email = User.objects.filter(email=email).order_by('date_joined')

        # Keep the first (oldest) user with the original email
        # Modify the email for all subsequent users
        for i, user in enumerate(users_with_email[1:], start=1):
            # Create a unique email by appending a number
            new_email = f"{email.split('@')[0]}_duplicate_{i}@{email.split('@')[1]}"

            # Ensure the new email is unique
            counter = 1
            while User.objects.filter(email=new_email).exists():
                counter += 1
                new_email = f"{email.split('@')[0]}_duplicate_{i}_{counter}@{email.split('@')[1]}"

            user.email = new_email
            user.save()

            print(f"Changed duplicate email from {email} to {new_email} for user {user.username}")


def reverse_fix_duplicate_emails(apps, schema_editor):
    """
    This migration is not easily reversible since we don't know the original
    duplicate emails. Manual intervention would be required.
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0033_add_chart_fields_to_blogpost'),
    ]

    operations = [
        migrations.RunPython(fix_duplicate_emails, reverse_fix_duplicate_emails),
    ]
