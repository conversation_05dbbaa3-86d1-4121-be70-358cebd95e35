# Generated by Django 5.2 on 2025-07-16 16:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0037_add_location_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='RecentlyViewedCar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(blank=True, help_text='For anonymous users', max_length=40)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('viewed_at', models.DateTimeField(auto_now_add=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recent_views', to='core.car')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='recently_viewed_cars', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-viewed_at'],
                'indexes': [models.Index(fields=['user', '-viewed_at'], name='core_recent_user_id_4084e5_idx'), models.Index(fields=['session_key', '-viewed_at'], name='core_recent_session_c5a767_idx'), models.Index(fields=['car', '-viewed_at'], name='core_recent_car_id_428a28_idx')],
                'unique_together': {('session_key', 'car'), ('user', 'car')},
            },
        ),
    ]
