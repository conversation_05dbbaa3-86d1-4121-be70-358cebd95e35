# Generated by Django 5.2 on 2025-07-15 13:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0032_add_pdf_fields_to_blogpost'),
    ]

    operations = [
        migrations.AddField(
            model_name='blogpost',
            name='chart_data',
            field=models.JSONField(blank=True, help_text='Chart configuration and data for infographics', null=True),
        ),
        migrations.AddField(
            model_name='blogpost',
            name='chart_type',
            field=models.CharField(blank=True, choices=[('bar', 'Bar Chart'), ('line', 'Line Chart'), ('pie', 'Pie Chart'), ('doughnut', 'Doughnut Chart'), ('radar', 'Radar Chart'), ('scatter', 'Scatter Plot'), ('bubble', 'Bubble Chart'), ('polar', 'Polar Area Chart')], help_text='Primary chart type for infographics', max_length=20),
        ),
    ]
