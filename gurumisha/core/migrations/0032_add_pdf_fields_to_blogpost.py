# Generated by Django 5.2 on 2025-07-15 13:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0031_add_opinion_polling_models'),
    ]

    operations = [
        migrations.AddField(
            model_name='blogpost',
            name='pdf_download_count',
            field=models.PositiveIntegerField(default=0, help_text='Number of PDF downloads'),
        ),
        migrations.AddField(
            model_name='blogpost',
            name='pdf_file',
            field=models.FileField(blank=True, help_text='PDF file for guides (max 10MB)', upload_to='content/guides/pdfs/%Y/%m/'),
        ),
        migrations.AddField(
            model_name='blogpost',
            name='pdf_file_size',
            field=models.PositiveIntegerField(blank=True, help_text='PDF file size in bytes', null=True),
        ),
    ]
