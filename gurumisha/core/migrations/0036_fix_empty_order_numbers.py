# Generated by Django 5.2 on 2025-07-16 09:13

from django.db import migrations
from django.utils import timezone
import uuid


def fix_empty_order_numbers(apps, schema_editor):
    """
    Fix any ImportOrder objects with empty order_numbers by generating new ones.
    """
    ImportOrder = apps.get_model('core', 'ImportOrder')

    # Find orders with empty order numbers
    empty_orders = ImportOrder.objects.filter(order_number='')

    for order in empty_orders:
        # Generate a unique order number
        new_order_number = f"IMP{timezone.now().year}{str(uuid.uuid4())[:8].upper()}"

        # Ensure uniqueness
        while ImportOrder.objects.filter(order_number=new_order_number).exists():
            new_order_number = f"IMP{timezone.now().year}{str(uuid.uuid4())[:8].upper()}"

        order.order_number = new_order_number
        order.save()

        print(f"Fixed empty order number for order ID {order.id}: {new_order_number}")


def reverse_fix_empty_order_numbers(apps, schema_editor):
    """
    This migration is not easily reversible since we don't know the original
    empty order numbers. Manual intervention would be required.
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0035_add_email_unique_constraint'),
    ]

    operations = [
        migrations.RunPython(fix_empty_order_numbers, reverse_fix_empty_order_numbers),
    ]
