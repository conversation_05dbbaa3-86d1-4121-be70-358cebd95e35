# Generated by Django 5.2 on 2025-07-15 12:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0030_allow_null_vendor_in_sparepart'),
    ]

    operations = [
        migrations.CreateModel(
            name='OpinionPoll',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question', models.CharField(help_text='The poll question', max_length=300)),
                ('poll_type', models.CharField(choices=[('single_choice', 'Single Choice'), ('multiple_choice', 'Multiple Choice'), ('rating', 'Rating Scale'), ('yes_no', 'Yes/No')], default='single_choice', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('allow_anonymous_voting', models.BooleanField(default=False)),
                ('show_results_before_voting', models.BooleanField(default=False)),
                ('multiple_votes_per_user', models.BooleanField(default=False)),
                ('start_date', models.DateTimeField(auto_now_add=True)),
                ('end_date', models.DateTimeField(blank=True, help_text='Leave blank for no end date', null=True)),
                ('total_votes', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('opinion_post', models.OneToOneField(limit_choices_to={'content_type': 'opinion'}, on_delete=django.db.models.deletion.CASCADE, related_name='poll', to='core.blogpost')),
            ],
            options={
                'verbose_name': 'Opinion Poll',
                'verbose_name_plural': 'Opinion Polls',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OpinionReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(choices=[(1, '1 - Strongly Disagree'), (2, '2 - Disagree'), (3, '3 - Neutral'), (4, '4 - Agree'), (5, '5 - Strongly Agree')])),
                ('review_text', models.TextField(blank=True, help_text='Optional review text')),
                ('is_approved', models.BooleanField(default=False)),
                ('is_featured', models.BooleanField(default=False)),
                ('helpful_votes', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('opinion_post', models.ForeignKey(limit_choices_to={'content_type': 'opinion'}, on_delete=django.db.models.deletion.CASCADE, related_name='opinion_reviews', to='core.blogpost')),
                ('reviewer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='opinion_reviews', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Opinion Review',
                'verbose_name_plural': 'Opinion Reviews',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PollOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.CharField(max_length=200)),
                ('order', models.PositiveIntegerField(default=0)),
                ('vote_count', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('poll', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='options', to='core.opinionpoll')),
            ],
            options={
                'verbose_name': 'Poll Option',
                'verbose_name_plural': 'Poll Options',
                'ordering': ['order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='PollVote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(blank=True, max_length=40, null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('option', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='core.polloption')),
                ('poll', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='core.opinionpoll')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='poll_votes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Poll Vote',
                'verbose_name_plural': 'Poll Votes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReviewHelpfulVote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_helpful', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('review', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='helpful_vote_records', to='core.opinionreview')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='review_helpful_votes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Review Helpful Vote',
                'verbose_name_plural': 'Review Helpful Votes',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='opinionreview',
            index=models.Index(fields=['opinion_post', 'is_approved'], name='core_opinio_opinion_ef7956_idx'),
        ),
        migrations.AddIndex(
            model_name='opinionreview',
            index=models.Index(fields=['reviewer', '-created_at'], name='core_opinio_reviewe_81a75c_idx'),
        ),
        migrations.AddIndex(
            model_name='opinionreview',
            index=models.Index(fields=['rating', 'is_approved'], name='core_opinio_rating_8fbb06_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='opinionreview',
            unique_together={('opinion_post', 'reviewer')},
        ),
        migrations.AlterUniqueTogether(
            name='polloption',
            unique_together={('poll', 'order')},
        ),
        migrations.AddConstraint(
            model_name='pollvote',
            constraint=models.UniqueConstraint(condition=models.Q(('user__isnull', False)), fields=('poll', 'user'), name='unique_user_poll_vote'),
        ),
        migrations.AddConstraint(
            model_name='pollvote',
            constraint=models.UniqueConstraint(condition=models.Q(('session_key__isnull', False)), fields=('poll', 'session_key'), name='unique_session_poll_vote'),
        ),
        migrations.AlterUniqueTogether(
            name='reviewhelpfulvote',
            unique_together={('review', 'user')},
        ),
    ]
