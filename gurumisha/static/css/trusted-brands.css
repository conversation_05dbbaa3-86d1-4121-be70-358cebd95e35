/* Enhanced Trusted Brands Section Styles */

/* Floating animation for background pattern */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-10px) rotate(1deg);
    }
    50% {
        transform: translateY(-20px) rotate(0deg);
    }
    75% {
        transform: translateY(-10px) rotate(-1deg);
    }
}

/* Pulse animation for award icon */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(220, 38, 38, 0.5);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 40px rgba(220, 38, 38, 0.8);
        transform: scale(1.05);
    }
}

/* Entrance animation for the entire section */
@keyframes entrance {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Brand card hover glow effect */
@keyframes brand-glow {
    0%, 100% {
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    }
    50% {
        box-shadow: 0 20px 60px rgba(220, 38, 38, 0.4);
    }
}

/* Staggered animation for brand cards */
@keyframes stagger-in {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Trust indicator icon rotation */
@keyframes icon-rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Apply animations */
.animate-entrance {
    animation: entrance 1s ease-out;
}

.animate-pulse {
    animation: pulse-glow 3s ease-in-out infinite;
}

/* Brand card staggered animations */
.trusted-brands-grid .group:nth-child(1) {
    animation: stagger-in 0.6s ease-out 0.1s both;
}

.trusted-brands-grid .group:nth-child(2) {
    animation: stagger-in 0.6s ease-out 0.2s both;
}

.trusted-brands-grid .group:nth-child(3) {
    animation: stagger-in 0.6s ease-out 0.3s both;
}

.trusted-brands-grid .group:nth-child(4) {
    animation: stagger-in 0.6s ease-out 0.4s both;
}

.trusted-brands-grid .group:nth-child(5) {
    animation: stagger-in 0.6s ease-out 0.5s both;
}

.trusted-brands-grid .group:nth-child(6) {
    animation: stagger-in 0.6s ease-out 0.6s both;
}

.trusted-brands-grid .group:nth-child(7) {
    animation: stagger-in 0.6s ease-out 0.7s both;
}

.trusted-brands-grid .group:nth-child(8) {
    animation: stagger-in 0.6s ease-out 0.8s both;
}

/* Enhanced hover effects for brand cards */
.trusted-brands-grid .group:hover {
    animation: brand-glow 2s ease-in-out infinite;
}

/* Trust indicator hover effects */
.trust-indicator:hover .icon {
    animation: icon-rotate 1s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .trusted-brands-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .trusted-brands-grid .group > div {
        padding: 1.5rem;
    }
    
    .trusted-brands-grid img {
        width: 4rem;
        height: 4rem;
    }
    
    .trusted-brands-grid h3 {
        font-size: 1rem;
        margin-top: 1rem;
    }
}

@media (max-width: 640px) {
    .trusted-brands-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .trusted-brands-grid .group > div {
        padding: 1rem;
    }
    
    .trusted-brands-grid img {
        width: 3rem;
        height: 3rem;
    }
    
    .trusted-brands-grid h3 {
        font-size: 0.875rem;
        margin-top: 0.75rem;
    }
}

/* Glassmorphism enhancement */
.glassmorphism-card {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glassmorphism-card:hover {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(220, 38, 38, 0.5);
    box-shadow: 0 12px 48px rgba(220, 38, 38, 0.2);
}

/* Text glow effect */
.text-glow {
    text-shadow: 0 0 10px rgba(220, 38, 38, 0.5);
}

/* Smooth transitions for all elements */
* {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar for the section */
.trusted-brands-section::-webkit-scrollbar {
    width: 8px;
}

.trusted-brands-section::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.trusted-brands-section::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #dc2626, #991b1b);
    border-radius: 4px;
}

.trusted-brands-section::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #ef4444, #dc2626);
}

/* Loading animation for brand images */
.brand-image {
    opacity: 0;
    animation: fadeInImage 0.8s ease-out forwards;
}

@keyframes fadeInImage {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Delay for each brand image */
.brand-image:nth-child(1) { animation-delay: 0.1s; }
.brand-image:nth-child(2) { animation-delay: 0.2s; }
.brand-image:nth-child(3) { animation-delay: 0.3s; }
.brand-image:nth-child(4) { animation-delay: 0.4s; }
.brand-image:nth-child(5) { animation-delay: 0.5s; }
.brand-image:nth-child(6) { animation-delay: 0.6s; }
.brand-image:nth-child(7) { animation-delay: 0.7s; }
.brand-image:nth-child(8) { animation-delay: 0.8s; }

/* Enhanced focus states for accessibility */
.group:focus-within {
    outline: 2px solid #dc2626;
    outline-offset: 4px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .glassmorphism-card {
        background: rgba(0, 0, 0, 0.8);
        border: 2px solid #ffffff;
    }
    
    .text-gray-300 {
        color: #ffffff !important;
    }
    
    .text-red-400 {
        color: #ff6b6b !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
