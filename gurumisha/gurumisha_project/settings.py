"""
Django settings for guru<PERSON>ha_project project.

Generated by 'django-admin startproject' using Django 5.2.4.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
from decouple import config
import os

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-q-k956cf0@y7d(jmvv7*e6!r4@cm6xc-ul6v5m96435ccpl8l9'

# SECURITY WARNING: don't run with debug turned on in production!
# SECURITY WARNING: don't run with debug turned on in production!
# To test 404 page, temporarily set DEBUG = False, then set back to True
DEBUG = True  # Set back to True for development

# Custom setting to test 404 pages in development
SHOW_CUSTOM_404 = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0', '*']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',
    'tailwind',
    'django_htmx',
    'core',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django_htmx.middleware.HtmxMiddleware',
    'core.middleware.EmailVerificationMiddleware',
    'core.middleware.SessionSecurityMiddleware',
    # Temporarily disabled to debug recursion issue
    # 'core.middleware.ActivityTrackingMiddleware',
    # 'core.middleware.AuditTrackingMiddleware',
    'core.middleware.ToastErrorHandlingMiddleware',
    'core.middleware.Custom404Middleware',
]

ROOT_URLCONF = 'gurumisha_project.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'gurumisha_project.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
        'OPTIONS': {
            'timeout': 30,  # 30 second timeout for database operations
            'init_command': 'PRAGMA journal_mode=WAL;',  # Use WAL mode for better concurrency
        },
        'CONN_MAX_AGE': 0,  # Don't persist connections to avoid locks
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Tailwind CSS
TAILWIND_APP_NAME = 'theme'

# Custom User Model
AUTH_USER_MODEL = 'core.User'

# Authentication URLs
LOGIN_URL = '/login/'
LOGIN_REDIRECT_URL = '/dashboard/'
LOGOUT_REDIRECT_URL = '/'

# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'wnyq jyuw mrqv phto'
DEFAULT_FROM_EMAIL = 'Gurumisha <<EMAIL>>'
SERVER_EMAIL = '<EMAIL>'

# Email Verification Settings
EMAIL_VERIFICATION_TIMEOUT_HOURS = 24
EMAIL_VERIFICATION_REQUIRED = True

# Promotion Email Settings
PROMOTION_EMAIL_ENABLED = True
WEEKLY_DIGEST_DAY = 1  # Monday (0=Monday, 6=Sunday)
HOT_DEAL_EMAIL_DELAY_HOURS = 2  # Hours to wait before sending hot deal emails
VENDOR_SUMMARY_DAY = 1  # Day of month to send vendor summaries

# Sites Framework
SITE_ID = 1

# Site URL for email links and redirects
SITE_URL = config('SITE_URL', default='https://gurumishamotors.com')

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# M-Pesa Configuration
# Environment setting (sandbox or production)
MPESA_ENVIRONMENT = config('MPESA_ENVIRONMENT', default='sandbox')

# Sandbox Configuration
MPESA_SANDBOX_CONSUMER_KEY = config('MPESA_SANDBOX_CONSUMER_KEY', default='')
MPESA_SANDBOX_CONSUMER_SECRET = config('MPESA_SANDBOX_CONSUMER_SECRET', default='')
MPESA_SANDBOX_BUSINESS_SHORT_CODE = config('MPESA_SANDBOX_BUSINESS_SHORT_CODE', default='174379')
MPESA_SANDBOX_PASSKEY = config('MPESA_SANDBOX_PASSKEY', default='')

# Production Configuration
MPESA_PRODUCTION_CONSUMER_KEY = config('MPESA_PRODUCTION_CONSUMER_KEY', default='')
MPESA_PRODUCTION_CONSUMER_SECRET = config('MPESA_PRODUCTION_CONSUMER_SECRET', default='')
MPESA_PRODUCTION_BUSINESS_SHORT_CODE = config('MPESA_PRODUCTION_BUSINESS_SHORT_CODE', default='')
MPESA_PRODUCTION_PASSKEY = config('MPESA_PRODUCTION_PASSKEY', default='')

# Current configuration based on environment
if MPESA_ENVIRONMENT == 'production':
    MPESA_CONSUMER_KEY = MPESA_PRODUCTION_CONSUMER_KEY
    MPESA_CONSUMER_SECRET = MPESA_PRODUCTION_CONSUMER_SECRET
    MPESA_BUSINESS_SHORT_CODE = MPESA_PRODUCTION_BUSINESS_SHORT_CODE
    MPESA_PASSKEY = MPESA_PRODUCTION_PASSKEY
else:
    MPESA_CONSUMER_KEY = MPESA_SANDBOX_CONSUMER_KEY
    MPESA_CONSUMER_SECRET = MPESA_SANDBOX_CONSUMER_SECRET
    MPESA_BUSINESS_SHORT_CODE = MPESA_SANDBOX_BUSINESS_SHORT_CODE
    MPESA_PASSKEY = MPESA_SANDBOX_PASSKEY

# Callback URLs - Dynamic based on environment
if DEBUG:
    # Development callback URL (use ngrok or similar for testing)
    MPESA_CALLBACK_URL = config('MPESA_CALLBACK_URL', default='https://your-ngrok-url.ngrok.io/payments/mpesa/callback/')
    MPESA_TIMEOUT_URL = config('MPESA_TIMEOUT_URL', default='https://your-ngrok-url.ngrok.io/payments/mpesa/timeout/')
else:
    # Production callback URLs
    MPESA_CALLBACK_URL = config('MPESA_CALLBACK_URL', default='https://yourdomain.com/payments/mpesa/callback/')
    MPESA_TIMEOUT_URL = config('MPESA_TIMEOUT_URL', default='https://yourdomain.com/payments/mpesa/timeout/')

# M-Pesa API URLs
MPESA_BASE_URL = {
    'sandbox': 'https://sandbox.safaricom.co.ke',
    'production': 'https://api.safaricom.co.ke'
}

# M-Pesa API Endpoints
MPESA_ENDPOINTS = {
    'oauth': '/oauth/v1/generate?grant_type=client_credentials',
    'stk_push': '/mpesa/stkpush/v1/processrequest',
    'stk_query': '/mpesa/stkpushquery/v1/query',
    'b2c': '/mpesa/b2c/v1/paymentrequest',
    'account_balance': '/mpesa/accountbalance/v1/query',
    'transaction_status': '/mpesa/transactionstatus/v1/query'
}

# M-Pesa Transaction Types
MPESA_TRANSACTION_TYPES = {
    'customer_paybill': 'CustomerPayBillOnline',
    'customer_buygoods': 'CustomerBuyGoodsOnline',
    'business_paybill': 'BusinessPayBill',
    'business_buygoods': 'BusinessBuyGoods',
    'salary_payment': 'SalaryPayment',
    'business_payment': 'BusinessPayment',
    'promotion_payment': 'PromotionPayment'
}

# M-Pesa Default Settings
MPESA_DEFAULT_TRANSACTION_TYPE = MPESA_TRANSACTION_TYPES['customer_paybill']
MPESA_DEFAULT_ACCOUNT_REFERENCE = 'GURUMISHA'
MPESA_DEFAULT_TRANSACTION_DESC = 'Payment for Gurumisha Motors'

# M-Pesa Validation Settings
MPESA_PHONE_NUMBER_REGEX = r'^254[0-9]{9}$'
MPESA_MIN_AMOUNT = 1
MPESA_MAX_AMOUNT = 70000

# M-Pesa Timeout Settings (in seconds)
MPESA_REQUEST_TIMEOUT = 30
MPESA_TOKEN_CACHE_TIMEOUT = 3600  # 1 hour

# M-Pesa Logging
MPESA_ENABLE_LOGGING = True
MPESA_LOG_LEVEL = 'INFO'
