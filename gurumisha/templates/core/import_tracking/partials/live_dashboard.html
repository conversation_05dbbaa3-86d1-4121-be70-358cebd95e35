<!-- Live Tracking Dashboard Component -->
<div class="live-tracking-dashboard" 
     hx-get="{% url 'core:import_tracking_dashboard_htmx' %}"
     hx-trigger="every 45s"
     hx-swap="innerHTML"
     hx-target="#dashboard-content"
     x-data="liveDashboard()"
     x-init="initializeDashboard()">
    
    <!-- Dashboard Header -->
    <div class="bg-gradient-to-r from-red-900 to-black text-white p-6 rounded-t-lg">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-semibold font-montserrat">Live Tracking Dashboard</h2>
                <p class="text-red-200 text-sm">Real-time updates for all your tracked orders</p>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-green-200 text-sm font-medium">Live Updates</span>
                </div>
                <div class="text-right">
                    <div class="text-sm text-red-200">Last Update</div>
                    <div class="text-xs text-red-300" x-text="lastUpdateTime">Just now</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div id="dashboard-content" class="bg-white rounded-b-lg">
        <div class="p-6">
            <!-- Statistics Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Total Tracked Orders -->
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 border border-blue-200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-car text-white text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-blue-600">Tracked Orders</p>
                            <p class="text-2xl font-bold text-blue-900" x-text="stats.totalOrders">{{ total_tracked_orders|default:0 }}</p>
                        </div>
                    </div>
                </div>

                <!-- Orders with Location -->
                <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 border border-green-200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-map-marker-alt text-white text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-green-600">With Location</p>
                            <p class="text-2xl font-bold text-green-900" x-text="stats.withLocation">{{ orders_with_location|default:0 }}</p>
                        </div>
                    </div>
                </div>

                <!-- Coverage Percentage -->
                <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-percentage text-white text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-purple-600">Coverage</p>
                            <p class="text-2xl font-bold text-purple-900" x-text="stats.coverage + '%'">
                                {% if total_tracked_orders > 0 %}
                                    {{ orders_with_location|floatformat:0|mul:100|div:total_tracked_orders|floatformat:0 }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Orders Grid -->
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 font-montserrat">Active Tracking</h3>
                    <button @click="refreshDashboard()" 
                            class="px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                            :disabled="refreshing">
                        <i class="fas fa-sync-alt mr-2" :class="{ 'animate-spin': refreshing }"></i>
                        <span x-text="refreshing ? 'Refreshing...' : 'Refresh All'">Refresh All</span>
                    </button>
                </div>

                <!-- Orders List -->
                <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" x-show="orders.length > 0">
                    <template x-for="order in orders" :key="order.order_number">
                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-all duration-200">
                            <!-- Order Header -->
                            <div class="flex items-start justify-between mb-3">
                                <div>
                                    <h4 class="font-semibold text-gray-900" x-text="order.order_number"></h4>
                                    <p class="text-sm text-gray-600" x-text="order.vehicle_details"></p>
                                </div>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                      :class="{
                                          'bg-blue-100 text-blue-800': order.status === 'in_transit',
                                          'bg-purple-100 text-purple-800': order.status === 'shipped',
                                          'bg-green-100 text-green-800': order.status === 'arrived_docked',
                                          'bg-yellow-100 text-yellow-800': !['in_transit', 'shipped', 'arrived_docked'].includes(order.status)
                                      }"
                                      x-text="order.status_display">
                                </span>
                            </div>

                            <!-- Location Status -->
                            <div class="mb-3">
                                <template x-if="order.current_location">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                        <span class="text-sm font-medium text-gray-900" x-text="order.current_location.name"></span>
                                    </div>
                                </template>
                                <template x-if="!order.current_location">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                        <span class="text-sm text-gray-500">Awaiting location update</span>
                                    </div>
                                </template>
                                <template x-if="order.last_update">
                                    <p class="text-xs text-gray-500 ml-4" x-text="'Updated ' + formatTimeAgo(order.last_update)"></p>
                                </template>
                            </div>

                            <!-- Progress Bar -->
                            <div class="mb-3">
                                <div class="flex justify-between text-xs text-gray-600 mb-1">
                                    <span>Progress</span>
                                    <span x-text="order.progress_percentage + '%'"></span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-gradient-to-r from-red-600 to-red-800 h-2 rounded-full transition-all duration-500" 
                                         :style="`width: ${order.progress_percentage}%`"></div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex space-x-2">
                                <a :href="`/import/tracking/${order.order_number}/`"
                                   class="flex-1 bg-red-600 text-white text-center py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors">
                                    <i class="fas fa-map mr-1"></i>View Map
                                </a>
                                <button @click="refreshOrder(order.order_number)"
                                        class="px-3 py-2 bg-gray-600 text-white rounded-lg text-sm hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                    </template>
                </div>

                <!-- Empty State -->
                <div x-show="orders.length === 0" class="text-center py-12">
                    <i class="fas fa-map-marked-alt text-gray-300 text-4xl mb-4"></i>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2">No Active Tracking</h4>
                    <p class="text-gray-600">Your orders with live tracking will appear here</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function liveDashboard() {
    return {
        lastUpdateTime: 'Just now',
        refreshing: false,
        stats: {
            totalOrders: {{ total_tracked_orders|default:0 }},
            withLocation: {{ orders_with_location|default:0 }},
            coverage: {% if total_tracked_orders > 0 %}{{ orders_with_location|floatformat:0|mul:100|div:total_tracked_orders|floatformat:0 }}{% else %}0{% endif %}
        },
        orders: [
            {% for order in orders %}
            {
                order_number: '{{ order.order_number }}',
                vehicle_details: '{{ order.vehicle_details }}',
                status: '{{ order.status }}',
                status_display: '{{ order.get_status_display }}',
                progress_percentage: {{ order.progress_percentage }},
                current_location: {% if order.current_latitude and order.current_longitude %}{
                    name: '{{ order.current_location_name|default:"Location Available" }}',
                    coordinates: '{{ order.current_coordinates_string }}',
                    google_maps_url: '{{ order.google_maps_url }}'
                }{% else %}null{% endif %},
                last_update: {% if order.last_location_update %}'{{ order.last_location_update.isoformat }}'{% else %}null{% endif %}
            }{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        
        initializeDashboard() {
            this.startTimeUpdates();
        },
        
        startTimeUpdates() {
            setInterval(() => {
                const now = new Date();
                this.lastUpdateTime = now.toLocaleTimeString();
            }, 1000);
        },
        
        refreshDashboard() {
            this.refreshing = true;
            
            // Trigger HTMX refresh
            const dashboard = document.querySelector('.live-tracking-dashboard');
            htmx.trigger(dashboard, 'refresh');
            
            setTimeout(() => {
                this.refreshing = false;
            }, 2000);
        },
        
        refreshOrder(orderNumber) {
            // Refresh individual order data
            fetch(`/import/tracking/${orderNumber}/live/`, {
                headers: {
                    'HX-Request': 'true',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
                }
            })
            .then(response => response.json())
            .then(data => {
                // Update order in the array
                const orderIndex = this.orders.findIndex(o => o.order_number === orderNumber);
                if (orderIndex !== -1) {
                    this.orders[orderIndex] = {
                        ...this.orders[orderIndex],
                        current_location: data.current_location,
                        progress_percentage: data.progress_percentage,
                        status_display: data.status_display,
                        last_update: data.last_update_timestamp
                    };
                }
            })
            .catch(error => {
                console.error('Error refreshing order:', error);
            });
        },
        
        formatTimeAgo(isoString) {
            if (!isoString) return 'never';
            
            const date = new Date(isoString);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            
            if (diffMins < 1) return 'just now';
            if (diffMins < 60) return `${diffMins} min ago`;
            
            const diffHours = Math.floor(diffMins / 60);
            if (diffHours < 24) return `${diffHours}h ago`;
            
            const diffDays = Math.floor(diffHours / 24);
            return `${diffDays}d ago`;
        },
        
        updateFromResponse(data) {
            // Update dashboard data from HTMX response
            if (data.total_tracked_orders !== undefined) {
                this.stats.totalOrders = data.total_tracked_orders;
            }
            if (data.orders_with_location !== undefined) {
                this.stats.withLocation = data.orders_with_location;
            }
            if (data.orders) {
                this.orders = data.orders;
            }
            
            // Recalculate coverage
            this.stats.coverage = this.stats.totalOrders > 0 
                ? Math.round((this.stats.withLocation / this.stats.totalOrders) * 100)
                : 0;
        }
    }
}

// Listen for HTMX responses to update Alpine.js data
document.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.xhr.responseURL && event.detail.xhr.responseURL.includes('dashboard/live/')) {
        try {
            const data = JSON.parse(event.detail.xhr.responseText);
            const dashboard = document.querySelector('.live-tracking-dashboard');
            const alpineData = Alpine.$data(dashboard);
            if (alpineData && alpineData.updateFromResponse) {
                alpineData.updateFromResponse(data);
            }
        } catch (e) {
            console.log('Response is not JSON, likely HTML template');
        }
    }
});
</script>

<style>
.live-tracking-dashboard {
    @apply shadow-xl rounded-lg overflow-hidden;
}

.live-tracking-dashboard .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
