{% extends 'base.html' %}
{% load static %}

{% block title %}Message System Debug{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h1 class="text-3xl font-bold text-harrier-dark font-montserrat mb-2">
                <i class="fas fa-bug text-harrier-red mr-3"></i>
                Message System Debug
            </h1>
            <p class="text-gray-600 font-raleway">
                Debug information for the messaging system. Current user: <strong>{{ user.username }}</strong> ({{ user.role|default:"No role" }})
            </p>
        </div>

        <!-- Test Popup Button -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 class="text-xl font-bold text-harrier-dark font-montserrat mb-4">
                <i class="fas fa-play text-green-600 mr-2"></i>
                Test Message Popup
            </h2>
            <p class="text-gray-600 font-raleway mb-4">
                Click the button below to manually trigger the message popup system.
            </p>
            <button onclick="testMessagePopup()" 
                    class="bg-gradient-to-r from-harrier-red to-harrier-dark text-white px-6 py-3 rounded-lg font-medium font-raleway hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                <i class="fas fa-rocket mr-2"></i>
                Test Popup Messages
            </button>
        </div>

        <!-- Active Popup Messages -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 class="text-xl font-bold text-harrier-dark font-montserrat mb-4">
                <i class="fas fa-list text-blue-600 mr-2"></i>
                Active Popup Messages ({{ popup_messages.count }})
            </h2>
            
            {% if popup_messages %}
                <div class="space-y-4">
                    {% for message in popup_messages %}
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="font-bold text-gray-900 font-montserrat">{{ message.title }}</h3>
                                <p class="text-sm text-gray-600 font-raleway mt-1">{{ message.excerpt|truncatewords:20 }}</p>
                                <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                    <span><i class="fas fa-tag mr-1"></i>{{ message.get_message_type_display }}</span>
                                    <span><i class="fas fa-users mr-1"></i>{{ message.get_target_audience_display }}</span>
                                    <span><i class="fas fa-star mr-1"></i>Priority: {{ message.priority }}</span>
                                    <span><i class="fas fa-calendar mr-1"></i>{{ message.created_at|date:"M d, Y" }}</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                {% if message.show_as_popup %}
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">Popup</span>
                                {% endif %}
                                {% if message.show_in_dashboard %}
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">Dashboard</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600 font-raleway">No active popup messages found.</p>
                </div>
            {% endif %}
        </div>

        <!-- Message Targeting Results -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 class="text-xl font-bold text-harrier-dark font-montserrat mb-4">
                <i class="fas fa-crosshairs text-purple-600 mr-2"></i>
                Message Targeting Results for {{ user.username }}
            </h2>
            
            {% if messages_for_user %}
                <div class="space-y-4">
                    {% for item in messages_for_user %}
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h3 class="font-bold text-gray-900 font-montserrat">{{ item.message.title }}</h3>
                                <div class="flex items-center space-x-4 mt-2">
                                    <div class="flex items-center">
                                        {% if item.should_show %}
                                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                            <span class="text-green-700 font-medium">Should Show</span>
                                        {% else %}
                                            <i class="fas fa-times-circle text-red-500 mr-2"></i>
                                            <span class="text-red-700 font-medium">Should Not Show</span>
                                        {% endif %}
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        {% if item.read_status %}
                                            <i class="fas fa-eye mr-1"></i>
                                            {{ item.read_status.action|title }} on {{ item.read_status.last_seen_at|date:"M d, Y H:i" }}
                                        {% else %}
                                            <i class="fas fa-eye-slash mr-1"></i>
                                            Not seen
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-500">
                                    Target: {{ item.message.get_target_audience_display }}
                                </div>
                                <div class="text-sm text-gray-500">
                                    Max displays: {{ item.message.max_displays_per_user }}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600 font-raleway">No messages to analyze for targeting.</p>
                </div>
            {% endif %}
        </div>

        <!-- API Test Section -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-bold text-harrier-dark font-montserrat mb-4">
                <i class="fas fa-code text-orange-600 mr-2"></i>
                API Test Results
            </h2>
            <div class="space-y-4">
                <div>
                    <button onclick="testPopupAPI()" 
                            class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium font-raleway hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-play mr-2"></i>
                        Test Popup API
                    </button>
                    <div id="popup-api-result" class="mt-2 p-3 bg-gray-100 rounded-lg hidden">
                        <pre class="text-sm text-gray-700"></pre>
                    </div>
                </div>
                
                <div>
                    <button onclick="testDashboardAPI()" 
                            class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium font-raleway hover:bg-green-700 transition-colors duration-200">
                        <i class="fas fa-play mr-2"></i>
                        Test Dashboard API
                    </button>
                    <div id="dashboard-api-result" class="mt-2 p-3 bg-gray-100 rounded-lg hidden">
                        <pre class="text-sm text-gray-700"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testMessagePopup() {
    console.log('Testing message popup...');
    
    // Use the global message integration if available
    if (window.messageIntegration) {
        console.log('Using message integration...');
        window.messageIntegration.checkForPopupMessages();
    } else {
        console.log('Message integration not found, using manual fetch...');
        // Manual fetch as fallback
        fetch('/messages/popup/', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        }).then(response => {
            console.log('Manual fetch response status:', response.status);
            return response.text();
        }).then(data => {
            console.log('Manual fetch response data:', data);
            if (data && typeof data === 'string' && !data.includes('"no_messages": true')) {
                // Create a temporary container and insert the popup
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = data;
                document.body.appendChild(tempDiv);
            } else {
                alert('No messages to display for current user.');
            }
        }).catch(error => {
            console.error('Manual fetch error:', error);
            alert('Error testing popup: ' + error.message);
        });
    }
}

function testPopupAPI() {
    const resultDiv = document.getElementById('popup-api-result');
    const preElement = resultDiv.querySelector('pre');
    
    resultDiv.classList.remove('hidden');
    preElement.textContent = 'Loading...';
    
    fetch('/messages/popup/', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'same-origin'
    }).then(response => {
        return response.text().then(text => ({
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            body: text
        }));
    }).then(data => {
        preElement.textContent = JSON.stringify(data, null, 2);
    }).catch(error => {
        preElement.textContent = 'Error: ' + error.message;
    });
}

function testDashboardAPI() {
    const resultDiv = document.getElementById('dashboard-api-result');
    const preElement = resultDiv.querySelector('pre');
    
    resultDiv.classList.remove('hidden');
    preElement.textContent = 'Loading...';
    
    fetch('/messages/dashboard/', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        },
        credentials: 'same-origin'
    }).then(response => {
        return response.text().then(text => ({
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            body: text
        }));
    }).then(data => {
        preElement.textContent = JSON.stringify(data, null, 2);
    }).catch(error => {
        preElement.textContent = 'Error: ' + error.message;
    });
}

// Auto-test on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Message debug page loaded');
    console.log('Message integration available:', !!window.messageIntegration);
    console.log('Global dismissMessage function available:', !!window.dismissMessage);
});
</script>
{% endblock %}
