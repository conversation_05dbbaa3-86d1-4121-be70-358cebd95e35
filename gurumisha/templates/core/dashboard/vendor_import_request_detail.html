{% extends 'base_dashboard.html' %}
{% load static %}

{% block dashboard_title %}Import Request #{{ import_request.id|stringformat:"05d" }}{% endblock %}
{% block page_title %}Import Request Details{% endblock %}
{% block page_description %}View and manage your import request{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <a href="{% url 'core:vendor_import_requests' %}" class="ml-1 text-sm font-medium text-gray-500 hover:text-harrier-red md:ml-2 font-raleway">Import Requests</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 font-raleway">#{{ import_request.id|stringformat:"05d" }}</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-harrier-red to-harrier-red-dark rounded-xl p-6 text-white mb-8 animate-fade-in-up">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold font-montserrat mb-2">
                    {{ import_request.year }} {{ import_request.brand }} {{ import_request.model }}
                </h1>
                <p class="text-blue-100 font-raleway">
                    <i class="fas fa-calendar mr-2"></i>Submitted on {{ import_request.created_at|date:"F d, Y" }}
                </p>
            </div>
            <div class="text-right">
                <div class="text-sm text-blue-100 mb-1 font-raleway">Request ID</div>
                <div class="text-2xl font-bold font-montserrat">#{{ import_request.id|stringformat:"05d" }}</div>
            </div>
        </div>
    </div>

    <!-- Status and Progress Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8 animate-fade-in-up" style="animation-delay: 0.1s;">
        <!-- Current Status -->
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200">
            <h3 class="text-lg font-bold text-harrier-dark font-montserrat mb-4">Current Status</h3>
            <div class="text-center">
                {% if import_request.status == 'pending' %}
                    <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-clock text-yellow-600 text-2xl"></i>
                    </div>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        Pending Review
                    </span>
                {% elif import_request.status == 'on_quotation' %}
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-file-invoice text-blue-600 text-2xl"></i>
                    </div>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        Quotation Provided
                    </span>
                {% elif import_request.status == 'processing' %}
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-cogs text-purple-600 text-2xl"></i>
                    </div>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                        Processing
                    </span>
                {% elif import_request.status == 'fee_paid' %}
                    <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-credit-card text-indigo-600 text-2xl"></i>
                    </div>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                        Payment Confirmed
                    </span>
                {% elif import_request.status == 'completed' %}
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                    </div>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        Completed
                    </span>
                {% elif import_request.status == 'cancelled' %}
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-times-circle text-red-600 text-2xl"></i>
                    </div>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                        Cancelled
                    </span>
                {% endif %}
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200">
            <h3 class="text-lg font-bold text-harrier-dark font-montserrat mb-4">Progress</h3>
            <div class="space-y-3">
                <div class="flex justify-between text-sm font-medium text-gray-700">
                    <span>Progress</span>
                    <span>
                        {% if import_request.status == 'pending' %}20%
                        {% elif import_request.status == 'on_quotation' %}40%
                        {% elif import_request.status == 'processing' %}60%
                        {% elif import_request.status == 'fee_paid' %}80%
                        {% elif import_request.status == 'completed' %}100%
                        {% elif import_request.status == 'cancelled' %}0%
                        {% endif %}
                    </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                    {% if import_request.status == 'pending' %}
                        <div class="bg-yellow-500 h-3 rounded-full transition-all duration-500" style="width: 20%"></div>
                    {% elif import_request.status == 'on_quotation' %}
                        <div class="bg-blue-500 h-3 rounded-full transition-all duration-500" style="width: 40%"></div>
                    {% elif import_request.status == 'processing' %}
                        <div class="bg-purple-500 h-3 rounded-full transition-all duration-500" style="width: 60%"></div>
                    {% elif import_request.status == 'fee_paid' %}
                        <div class="bg-indigo-500 h-3 rounded-full transition-all duration-500" style="width: 80%"></div>
                    {% elif import_request.status == 'completed' %}
                        <div class="bg-green-500 h-3 rounded-full transition-all duration-500" style="width: 100%"></div>
                    {% elif import_request.status == 'cancelled' %}
                        <div class="bg-red-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                    {% endif %}
                </div>
                <div class="text-xs text-gray-500 font-raleway">
                    {% if import_request.status == 'pending' %}
                        Your request is being reviewed by our team
                    {% elif import_request.status == 'on_quotation' %}
                        Quotation has been provided for your review
                    {% elif import_request.status == 'processing' %}
                        Your import is being processed
                    {% elif import_request.status == 'fee_paid' %}
                        Payment confirmed, finalizing import
                    {% elif import_request.status == 'completed' %}
                        Import process completed successfully
                    {% elif import_request.status == 'cancelled' %}
                        Request has been cancelled
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200">
            <h3 class="text-lg font-bold text-harrier-dark font-montserrat mb-4">Quick Actions</h3>
            <div class="space-y-3">
                {% if import_request.status == 'completed' and import_order and import_order.order_number %}
                    <a href="{% url 'core:import_order_detail' import_order.order_number %}"
                       class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all duration-200 font-medium">
                        <i class="fas fa-truck mr-2"></i>Track Order
                    </a>
                {% elif import_request.status == 'completed' and import_order %}
                    <span class="w-full inline-flex items-center justify-center px-4 py-2 bg-gray-400 text-white rounded-lg cursor-not-allowed font-medium">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Order Invalid
                    </span>
                {% endif %}
                
                <button class="w-full inline-flex items-center justify-center px-4 py-2 bg-harrier-red text-white rounded-lg hover:bg-harrier-red-dark transition-all duration-200 font-medium"
                        onclick="window.print()">
                    <i class="fas fa-print mr-2"></i>Print Details
                </button>
                
                <a href="{% url 'core:vendor_import_requests' %}" 
                   class="w-full inline-flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-all duration-200 font-medium">
                    <i class="fas fa-arrow-left mr-2"></i>Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Detailed Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 animate-fade-in-up" style="animation-delay: 0.2s;">
        <!-- Vehicle Information -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat flex items-center">
                    <i class="fas fa-car mr-3 text-blue-600"></i>
                    Vehicle Details
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="text-sm font-medium text-gray-700 font-raleway">Brand</label>
                        <p class="text-harrier-dark font-semibold font-montserrat">{{ import_request.brand }}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-700 font-raleway">Model</label>
                        <p class="text-harrier-dark font-semibold font-montserrat">{{ import_request.model }}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-700 font-raleway">Year</label>
                        <p class="text-harrier-dark font-semibold font-montserrat">{{ import_request.year }}</p>
                    </div>
                    <div>
                        <label class="text-sm font-medium text-gray-700 font-raleway">Preferred Color</label>
                        <p class="text-harrier-dark font-semibold font-montserrat">{{ import_request.preferred_color|default:'Not specified' }}</p>
                    </div>
                </div>
                
                {% if import_request.special_requirements %}
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <label class="text-sm font-medium text-gray-700 font-raleway">Special Requirements</label>
                    <p class="text-gray-600 font-raleway mt-1">{{ import_request.special_requirements }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Import Information -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat flex items-center">
                    <i class="fas fa-globe mr-3 text-green-600"></i>
                    Import Details
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div>
                        <label class="text-sm font-medium text-gray-700 font-raleway">Origin Country</label>
                        <p class="text-harrier-dark font-semibold font-montserrat">{{ import_request.origin_country }}</p>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="text-sm font-medium text-gray-700 font-raleway">Min Budget</label>
                            <p class="text-harrier-dark font-semibold font-montserrat">KSh {{ import_request.budget_min|floatformat:0 }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-700 font-raleway">Max Budget</label>
                            <p class="text-harrier-dark font-semibold font-montserrat">KSh {{ import_request.budget_max|floatformat:0 }}</p>
                        </div>
                    </div>
                    
                    {% if import_request.estimated_cost %}
                    <div class="pt-4 border-t border-gray-200">
                        <label class="text-sm font-medium text-gray-700 font-raleway">Estimated Cost</label>
                        <p class="text-green-600 font-bold text-lg font-montserrat">KSh {{ import_request.estimated_cost|floatformat:0 }}</p>
                    </div>
                    {% endif %}
                    
                    {% if import_request.estimated_delivery %}
                    <div>
                        <label class="text-sm font-medium text-gray-700 font-raleway">Estimated Delivery</label>
                        <p class="text-harrier-dark font-semibold font-montserrat">{{ import_request.estimated_delivery|date:"F d, Y" }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Timeline and Notes Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8 animate-fade-in-up" style="animation-delay: 0.3s;">
        <!-- Status Timeline -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-purple-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat flex items-center">
                    <i class="fas fa-history mr-3 text-purple-600"></i>
                    Status Timeline
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <!-- Timeline items -->
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-plus text-green-600 text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900 font-montserrat">Request Submitted</p>
                            <p class="text-xs text-gray-500 font-raleway">{{ import_request.created_at|date:"F d, Y \a\t H:i" }}</p>
                        </div>
                    </div>

                    {% if import_request.status != 'pending' %}
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-eye text-blue-600 text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900 font-montserrat">Under Review</p>
                            <p class="text-xs text-gray-500 font-raleway">Request is being processed</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if import_request.status == 'on_quotation' or import_request.status == 'processing' or import_request.status == 'fee_paid' or import_request.status == 'completed' %}
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-file-invoice text-yellow-600 text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900 font-montserrat">Quotation Provided</p>
                            <p class="text-xs text-gray-500 font-raleway">Pricing and details shared</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if import_request.status == 'processing' or import_request.status == 'fee_paid' or import_request.status == 'completed' %}
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-cogs text-purple-600 text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900 font-montserrat">Processing Started</p>
                            <p class="text-xs text-gray-500 font-raleway">Import process initiated</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if import_request.status == 'completed' %}
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-green-600 text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900 font-montserrat">Import Completed</p>
                            <p class="text-xs text-gray-500 font-raleway">{{ import_request.updated_at|date:"F d, Y \a\t H:i" }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-orange-50 to-red-50 px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat flex items-center">
                    <i class="fas fa-info-circle mr-3 text-orange-600"></i>
                    Additional Information
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    {% if import_request.tracking_number %}
                    <div>
                        <label class="text-sm font-medium text-gray-700 font-raleway">Tracking Number</label>
                        <p class="text-harrier-dark font-semibold font-montserrat">{{ import_request.tracking_number }}</p>
                    </div>
                    {% endif %}

                    <div>
                        <label class="text-sm font-medium text-gray-700 font-raleway">Last Updated</label>
                        <p class="text-gray-600 font-raleway">{{ import_request.updated_at|date:"F d, Y \a\t H:i" }}</p>
                    </div>

                    {% if import_request.admin_notes %}
                    <div class="pt-4 border-t border-gray-200">
                        <label class="text-sm font-medium text-gray-700 font-raleway">Notes from Team</label>
                        <div class="mt-2 p-3 bg-gray-50 rounded-lg">
                            <p class="text-gray-600 font-raleway text-sm">{{ import_request.admin_notes }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Contact Information -->
                    <div class="pt-4 border-t border-gray-200">
                        <label class="text-sm font-medium text-gray-700 font-raleway">Need Help?</label>
                        <div class="mt-2 space-y-2">
                            <p class="text-sm text-gray-600 font-raleway">
                                <i class="fas fa-phone mr-2 text-harrier-red"></i>
                                Call us: +254 700 000 000
                            </p>
                            <p class="text-sm text-gray-600 font-raleway">
                                <i class="fas fa-envelope mr-2 text-harrier-red"></i>
                                Email: <EMAIL>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
// Add any specific JavaScript for the detail page here
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh status every 30 seconds if not completed
    {% if import_request.status != 'completed' and import_request.status != 'cancelled' %}
    setInterval(() => {
        location.reload();
    }, 30000);
    {% endif %}
});
</script>
{% endblock %}
