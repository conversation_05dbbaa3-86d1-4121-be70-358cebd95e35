{% load static %}

<!-- Opinion Review Form -->
<div class="opinion-review-container animate-fade-in-up">
    {% if success_message %}
    <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-2"></i>
            <span class="text-green-700 font-medium font-raleway">{{ success_message }}</span>
        </div>
    </div>
    {% endif %}

    <div class="glassmorphism-card p-6">
        <div class="review-header mb-6">
            <h3 class="text-xl font-bold text-harrier-dark font-montserrat mb-2">
                <i class="fas fa-star text-harrier-red mr-2"></i>
                Rate This Opinion
            </h3>
            <p class="text-gray-600 font-raleway">Share your thoughts and help others understand different perspectives</p>
        </div>

        {% if avg_rating > 0 %}
        <div class="current-rating mb-6 p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center justify-between">
                <div>
                    <span class="text-sm text-gray-600 font-raleway">Average Rating:</span>
                    <div class="flex items-center mt-1">
                        {% for i in "12345" %}
                            {% if forloop.counter <= avg_rating %}
                                <i class="fas fa-star text-yellow-400"></i>
                            {% else %}
                                <i class="far fa-star text-gray-300"></i>
                            {% endif %}
                        {% endfor %}
                        <span class="ml-2 text-sm font-medium text-gray-700">{{ avg_rating }}/5</span>
                    </div>
                </div>
                <div class="text-right">
                    <span class="text-sm text-gray-600 font-raleway">{{ total_reviews }} review{{ total_reviews|pluralize }}</span>
                </div>
            </div>
        </div>
        {% endif %}

        {% if user.is_authenticated %}
        <form hx-post="{% url 'core:opinion_review_submit' post.id %}"
              hx-target="#opinion-review-container"
              hx-swap="outerHTML"
              class="review-form">
            {% csrf_token %}
            
            <!-- Rating Selection -->
            <div class="form-group mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-3 font-raleway">
                    Your Rating <span class="text-red-500">*</span>
                </label>
                <div class="rating-stars flex items-center space-x-1">
                    {% for i in "12345" %}
                    <button type="button" 
                            class="star-btn text-2xl text-gray-300 hover:text-yellow-400 transition-colors duration-200"
                            data-rating="{{ forloop.counter }}"
                            onclick="setRating({{ forloop.counter }})">
                        <i class="far fa-star"></i>
                    </button>
                    {% endfor %}
                    <span class="ml-3 text-sm text-gray-600 font-raleway" id="rating-text">Click to rate</span>
                </div>
                <input type="hidden" name="rating" id="rating-input" required>
            </div>

            <!-- Review Text -->
            <div class="form-group mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">
                    Your Review (Optional)
                </label>
                <textarea name="review_text" 
                          rows="4" 
                          class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-300 font-raleway"
                          placeholder="Share your thoughts about this opinion...">{% if review %}{{ review.review_text }}{% endif %}</textarea>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
                <button type="submit" 
                        class="admin-request-add-btn"
                        id="submit-review-btn"
                        disabled>
                    <i class="fas fa-paper-plane mr-2"></i>
                    {% if review %}Update Review{% else %}Submit Review{% endif %}
                </button>
            </div>
        </form>
        {% else %}
        <div class="text-center py-8">
            <i class="fas fa-user-lock text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-600 font-raleway mb-4">Please log in to rate and review this opinion</p>
            <a href="{% url 'core:login' %}" class="admin-request-add-btn">
                <i class="fas fa-sign-in-alt mr-2"></i>
                Log In
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
function setRating(rating) {
    // Update hidden input
    document.getElementById('rating-input').value = rating;
    
    // Update star display
    const stars = document.querySelectorAll('.star-btn i');
    const ratingText = document.getElementById('rating-text');
    const submitBtn = document.getElementById('submit-review-btn');
    
    stars.forEach((star, index) => {
        if (index < rating) {
            star.className = 'fas fa-star';
            star.parentElement.classList.remove('text-gray-300');
            star.parentElement.classList.add('text-yellow-400');
        } else {
            star.className = 'far fa-star';
            star.parentElement.classList.remove('text-yellow-400');
            star.parentElement.classList.add('text-gray-300');
        }
    });
    
    // Update rating text
    const ratingLabels = {
        1: 'Strongly Disagree',
        2: 'Disagree', 
        3: 'Neutral',
        4: 'Agree',
        5: 'Strongly Agree'
    };
    
    ratingText.textContent = ratingLabels[rating];
    
    // Enable submit button
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    }
}

// Set existing rating if editing
{% if review %}
document.addEventListener('DOMContentLoaded', function() {
    setRating({{ review.rating }});
});
{% endif %}
</script>

<style>
.glassmorphism-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.star-btn:hover {
    transform: scale(1.1);
}

.admin-request-add-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
</style>
