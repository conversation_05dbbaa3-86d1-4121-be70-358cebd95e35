{% extends 'base.html' %}
{% load static %}

{% block title %}Resources - <PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Modern Resources Page Redesign */
    :root {
        --gradient-primary: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
        --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        --gradient-accent: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.08);
        --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.12);
        --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.15);
        --border-radius-lg: 20px;
        --border-radius-xl: 24px;
        --animation-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    /* Revolutionary Hero Section */
    .resources-hero {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 30%, #334155 70%, #475569 100%);
        position: relative;
        overflow: hidden;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .resources-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(220, 38, 38, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(220, 38, 38, 0.1) 0%, transparent 50%);
        z-index: 0;
    }

    .resources-hero::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            linear-gradient(135deg, rgba(220, 38, 38, 0.05) 0%, rgba(15, 23, 42, 0.9) 100%),
            url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        z-index: 1;
    }

    .hero-content {
        position: relative;
        z-index: 2;
    }

    .hero-title {
        font-size: clamp(3rem, 10vw, 6rem);
        font-weight: 900;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 30%, #e2e8f0 70%, #cbd5e1 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        line-height: 1.1;
        margin-bottom: 2rem;
        letter-spacing: -0.02em;
        position: relative;
    }

    .hero-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: linear-gradient(90deg, transparent 0%, #dc2626 50%, transparent 100%);
        border-radius: 2px;
    }

    .hero-subtitle {
        font-size: clamp(1.25rem, 4vw, 1.75rem);
        color: rgba(255, 255, 255, 0.95);
        font-weight: 400;
        margin-bottom: 3rem;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
        line-height: 1.6;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Revolutionary Search Experience */
    .search-container {
        position: relative;
        max-width: 700px;
        margin: 0 auto;
    }

    .search-input {
        width: 100%;
        padding: 1.5rem 2rem 1.5rem 4.5rem;
        font-size: 1.25rem;
        border: none;
        border-radius: 25px;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(30px);
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
        transition: all 0.4s var(--animation-spring);
        font-family: 'Raleway', sans-serif;
        font-weight: 500;
    }

    .search-input:focus {
        outline: none;
        box-shadow:
            0 0 0 4px rgba(220, 38, 38, 0.15),
            0 25px 80px rgba(0, 0, 0, 0.2),
            0 0 0 1px rgba(220, 38, 38, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 1);
        background: rgba(255, 255, 255, 1);
        transform: translateY(-4px) scale(1.02);
    }

    .search-input::placeholder {
        color: #9ca3af;
        font-weight: 400;
    }

    .search-icon {
        position: absolute;
        left: 2rem;
        top: 50%;
        transform: translateY(-50%);
        color: #dc2626;
        font-size: 1.5rem;
        transition: all 0.3s ease;
    }

    .search-input:focus + .search-icon {
        color: #b91c1c;
        transform: translateY(-50%) scale(1.1);
    }

    .search-button {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 18px;
        font-weight: 700;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s var(--animation-spring);
        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .search-button:hover {
        transform: translateY(-50%) translateY(-3px) scale(1.05);
        box-shadow: 0 12px 35px rgba(220, 38, 38, 0.5);
    }

    .search-button:active {
        transform: translateY(-50%) translateY(-1px) scale(0.98);
    }

    /* Revolutionary Filter Section */
    .filter-section {
        background:
            linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%),
            url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23dc2626' fill-opacity='0.02'%3E%3Cpolygon points='50 0 60 40 100 50 60 60 50 100 40 60 0 50 40 40'/%3E%3C/g%3E%3C/svg%3E");
        padding: 4rem 0;
        position: relative;
        overflow: hidden;
    }

    .filter-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 10% 20%, rgba(220, 38, 38, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 90% 80%, rgba(220, 38, 38, 0.03) 0%, transparent 50%);
        z-index: 0;
    }

    .filter-container {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(30px);
        border-radius: 30px;
        padding: 3rem;
        box-shadow:
            0 25px 80px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 1;
    }

    .filter-tabs {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
        margin-bottom: 3rem;
    }

    .filter-tab {
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1.25rem 2rem;
        font-weight: 700;
        font-size: 0.875rem;
        border-radius: 20px;
        transition: all 0.4s var(--animation-spring);
        font-family: 'Montserrat', sans-serif;
        cursor: pointer;
        user-select: none;
        position: relative;
        background: rgba(255, 255, 255, 0.9);
        color: #64748b;
        border: 2px solid rgba(226, 232, 240, 0.5);
        text-decoration: none;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        overflow: hidden;
    }

    .filter-tab::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s ease;
    }

    .filter-tab:hover::before {
        left: 100%;
    }

    .filter-tab:hover {
        background: rgba(220, 38, 38, 0.08);
        color: #dc2626;
        transform: translateY(-4px) scale(1.05);
        box-shadow: 0 15px 40px rgba(220, 38, 38, 0.2);
        border-color: rgba(220, 38, 38, 0.3);
    }

    .filter-tab.active {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
        color: white;
        box-shadow: 0 8px 30px rgba(220, 38, 38, 0.4);
        transform: translateY(-4px) scale(1.05);
        border-color: transparent;
    }

    .filter-tab.active::before {
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    }

    .filter-tab i {
        font-size: 1.125rem;
        transition: transform 0.3s ease;
    }

    .filter-tab:hover i,
    .filter-tab.active i {
        transform: scale(1.1);
    }

    /* Advanced Filters */
    .advanced-filters {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid rgba(229, 231, 235, 0.5);
    }

    .filter-group {
        position: relative;
    }

    .filter-select {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid rgba(229, 231, 235, 0.5);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.9);
        font-size: 0.875rem;
        transition: all 0.2s ease;
        font-family: 'Raleway', sans-serif;
    }

    .filter-select:focus {
        outline: none;
        border-color: #dc2626;
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }

    /* Content Statistics */
    .content-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid rgba(229, 231, 235, 0.5);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-soft);
        border-color: rgba(220, 38, 38, 0.2);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #dc2626;
        font-family: 'Montserrat', sans-serif;
        line-height: 1;
    }

    .stat-label {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;
        margin-top: 0.5rem;
        font-family: 'Raleway', sans-serif;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .filter-tabs {
            flex-direction: column;
            align-items: stretch;
        }

        .filter-tab {
            justify-content: center;
        }

        .advanced-filters {
            grid-template-columns: 1fr;
        }

        .content-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    /* Animation Classes */
    .animate-fade-in-up {
        animation: fadeInUp 0.6s ease-out forwards;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-scale-in {
        animation: scaleIn 0.4s var(--animation-spring) forwards;
    }

    @keyframes scaleIn {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* Enhanced Content Type Features */
    .content-features {
        background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.9) 100%);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 16px;
        border: 1px solid rgba(226, 232, 240, 0.6);
    }

    .feature-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .feature-badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .feature-badge.poll {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        color: #1e40af;
    }

    .feature-badge.pdf {
        background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
        color: #dc2626;
    }

    .feature-badge.chart {
        background: linear-gradient(135deg, #f3e8ff 0%, #ddd6fe 100%);
        color: #7c3aed;
    }

    .feature-badge.interactive {
        background: linear-gradient(135deg, #ecfdf5 0%, #bbf7d0 100%);
        color: #059669;
    }

    /* Enhanced Filter Tabs */
    .filter-tab {
        position: relative;
        overflow: hidden;
    }

    .filter-tab::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .filter-tab:hover::before {
        left: 100%;
    }

    .filter-tab.active {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    /* Content Type Icons */
    .content-type-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        transition: all 0.3s ease;
    }

    .content-type-icon.opinion {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        color: #1e40af;
    }

    .content-type-icon.guide {
        background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
        color: #dc2626;
    }

    .content-type-icon.infographic {
        background: linear-gradient(135deg, #f3e8ff 0%, #ddd6fe 100%);
        color: #7c3aed;
    }

    .content-type-icon.article {
        background: linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%);
        color: #059669;
    }

    /* Feature Showcase Cards */
    .feature-showcase-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(226, 232, 240, 0.6);
        border-radius: 16px;
        padding: 24px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .feature-showcase-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(220, 38, 38, 0.05), transparent);
        transition: left 0.6s ease;
    }

    .feature-showcase-card:hover::before {
        left: 100%;
    }

    .feature-showcase-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border-color: rgba(220, 38, 38, 0.2);
    }

    /* Enhanced Resource Cards */
    .resource-card {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .resource-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s ease;
        z-index: 1;
    }

    .resource-card:hover::before {
        left: 100%;
    }

    .resource-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    }

    /* Quick Action Buttons */
    .quick-action-btn {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        font-size: 0.75rem;
        font-weight: 500;
        border-radius: 20px;
        transition: all 0.2s ease;
        text-decoration: none;
    }

    .quick-action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .quick-action-btn.pdf {
        background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .quick-action-btn.chart {
        background: linear-gradient(135deg, #f3e8ff 0%, #ddd6fe 100%);
        color: #7c3aed;
        border: 1px solid #ddd6fe;
    }

    .quick-action-btn.poll {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        color: #1e40af;
        border: 1px solid #bfdbfe;
    }
</style>
{% endblock %}

{% block content %}
<!-- Revolutionary Hero Section -->
<section class="resources-hero min-h-[80vh] flex items-center justify-center">
    <div class="hero-content max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-fade-in-up" style="animation-delay: 0.2s;">
            <h1 class="hero-title font-montserrat">
                AUTOMOTIVE
                <br>
                <span style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 50%, #f87171 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    RESOURCES
                </span>
            </h1>
            <p class="hero-subtitle font-raleway max-w-4xl mx-auto">
                Discover expert insights, interactive polls, downloadable PDF guides, data visualizations, and comprehensive automotive analysis to make informed decisions
            </p>
        </div>

        <!-- Enhanced Search Experience -->
        <div class="animate-fade-in-up search-container" style="animation-delay: 0.4s;">
            <form hx-get="{% url 'core:resources_live_search' %}"
                  hx-target="#resources-content"
                  hx-trigger="submit, keyup delay:300ms from:input[name='search']"
                  hx-indicator="#search-loading">
                <div class="relative">
                    <input type="text"
                           name="search"
                           placeholder="Search articles, polls, PDF guides, charts, and insights..."
                           class="search-input"
                           value="{{ request.GET.search }}"
                           autocomplete="off">
                    <i class="search-icon fas fa-search"></i>
                    <button type="submit" class="search-button">
                        <span id="search-loading" class="htmx-indicator">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                        <span class="search-text">Search</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Enhanced Features Showcase Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-harrier-dark font-montserrat mb-4">
                Enhanced Learning Experience
            </h2>
            <p class="text-lg text-gray-600 font-raleway max-w-3xl mx-auto">
                Discover our new interactive features designed to provide you with comprehensive automotive knowledge
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Interactive Polls Feature -->
            <div class="feature-showcase-card group">
                <div class="content-type-icon opinion group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-poll text-2xl"></i>
                </div>
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat mb-2">Interactive Polls</h3>
                <p class="text-gray-600 font-raleway text-sm mb-4">
                    Participate in opinion polls, share your views, and see real-time results from the automotive community.
                </p>
                <div class="flex items-center text-xs text-blue-600">
                    <i class="fas fa-vote-yea mr-2"></i>
                    <span>Vote & Review</span>
                </div>
            </div>

            <!-- PDF Guides Feature -->
            <div class="feature-showcase-card group">
                <div class="content-type-icon guide group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-file-pdf text-2xl"></i>
                </div>
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat mb-2">Downloadable Guides</h3>
                <p class="text-gray-600 font-raleway text-sm mb-4">
                    Access comprehensive PDF guides that you can download, save, and reference anytime, anywhere.
                </p>
                <div class="flex items-center text-xs text-red-600">
                    <i class="fas fa-download mr-2"></i>
                    <span>Download & Save</span>
                </div>
            </div>

            <!-- Interactive Charts Feature -->
            <div class="feature-showcase-card group">
                <div class="content-type-icon infographic group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-chart-line text-2xl"></i>
                </div>
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat mb-2">Data Visualizations</h3>
                <p class="text-gray-600 font-raleway text-sm mb-4">
                    Explore interactive charts and graphs that make complex automotive data easy to understand.
                </p>
                <div class="flex items-center text-xs text-purple-600">
                    <i class="fas fa-mouse-pointer mr-2"></i>
                    <span>Interactive & Exportable</span>
                </div>
            </div>

            <!-- Expert Articles Feature -->
            <div class="feature-showcase-card group">
                <div class="content-type-icon article group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-newspaper text-2xl"></i>
                </div>
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat mb-2">Expert Analysis</h3>
                <p class="text-gray-600 font-raleway text-sm mb-4">
                    Read in-depth articles and analysis from automotive experts and industry professionals.
                </p>
                <div class="flex items-center text-xs text-green-600">
                    <i class="fas fa-user-tie mr-2"></i>
                    <span>Expert Insights</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary Filter & Navigation Section -->
<section class="filter-section">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="filter-container animate-scale-in" style="animation-delay: 0.1s;">
            <!-- Content Statistics -->
            <div class="content-stats">
                <div class="stat-card">
                    <div class="stat-value">{{ total_posts|default:0 }}</div>
                    <div class="stat-label">Total Resources</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ featured_count|default:0 }}</div>
                    <div class="stat-label">Featured</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ categories_count|default:0 }}</div>
                    <div class="stat-label">Categories</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ this_month_count|default:0 }}</div>
                    <div class="stat-label">This Month</div>
                </div>
            </div>

            <!-- Primary Content Type Filters -->
            <div class="filter-tabs">
                <button class="filter-tab active" data-filter="all"
                        hx-get="{% url 'core:resources_live_search' %}"
                        hx-target="#resources-content"
                        hx-include="[name='search'], [name='category'], [name='tag']">
                    <i class="fas fa-th-large"></i>
                    <span>All Content</span>
                </button>
                <button class="filter-tab" data-filter="article"
                        hx-get="{% url 'core:resources_live_search' %}?content_type=article"
                        hx-target="#resources-content"
                        hx-include="[name='search'], [name='category'], [name='tag']">
                    <i class="fas fa-newspaper"></i>
                    <span>Articles</span>
                </button>
                <button class="filter-tab" data-filter="guide"
                        hx-get="{% url 'core:resources_live_search' %}?content_type=guide"
                        hx-target="#resources-content"
                        hx-include="[name='search'], [name='category'], [name='tag']"
                        title="Comprehensive guides with downloadable PDFs">
                    <i class="fas fa-file-pdf"></i>
                    <span>Guides & PDFs</span>
                </button>
                <button class="filter-tab" data-filter="infographic"
                        hx-get="{% url 'core:resources_live_search' %}?content_type=infographic"
                        hx-target="#resources-content"
                        hx-include="[name='search'], [name='category'], [name='tag']"
                        title="Data visualizations and interactive charts">
                    <i class="fas fa-chart-line"></i>
                    <span>Data & Charts</span>
                </button>
                <button class="filter-tab" data-filter="opinion"
                        hx-get="{% url 'core:resources_live_search' %}?content_type=opinion"
                        hx-target="#resources-content"
                        hx-include="[name='search'], [name='category'], [name='tag']"
                        title="Opinion pieces with interactive polls and reviews">
                    <i class="fas fa-poll"></i>
                    <span>Opinions & Polls</span>
                </button>
                <button class="filter-tab" data-filter="news"
                        hx-get="{% url 'core:resources_live_search' %}?content_type=news"
                        hx-target="#resources-content"
                        hx-include="[name='search'], [name='category'], [name='tag']">
                    <i class="fas fa-rss"></i>
                    <span>News</span>
                </button>
                <button class="filter-tab" data-filter="review"
                        hx-get="{% url 'core:resources_live_search' %}?content_type=review"
                        hx-target="#resources-content"
                        hx-include="[name='search'], [name='category'], [name='tag']">
                    <i class="fas fa-star"></i>
                    <span>Reviews</span>
                </button>
            </div>

            <!-- Advanced Filters -->
            <div class="advanced-filters">
                <div class="filter-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <select name="category" class="filter-select"
                            hx-get="{% url 'core:resources_live_search' %}"
                            hx-target="#resources-content"
                            hx-include="[name='search'], [name='content_type'], [name='tag']">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category.slug }}" {% if current_category == category.slug %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="filter-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tag</label>
                    <select name="tag" class="filter-select"
                            hx-get="{% url 'core:resources_live_search' %}"
                            hx-target="#resources-content"
                            hx-include="[name='search'], [name='content_type'], [name='category']">
                        <option value="">All Tags</option>
                        {% for tag in popular_tags %}
                            <option value="{{ tag.slug }}" {% if current_tag == tag.slug %}selected{% endif %}>
                                {{ tag.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>



                <div class="filter-group">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                    <select name="sort" class="filter-select"
                            hx-get="{% url 'core:resources_live_search' %}"
                            hx-target="#resources-content"
                            hx-include="[name='search'], [name='content_type'], [name='category'], [name='tag']">
                        <option value="newest" {% if current_sort == 'newest' %}selected{% endif %}>Newest First</option>
                        <option value="oldest" {% if current_sort == 'oldest' %}selected{% endif %}>Oldest First</option>
                        <option value="popular" {% if current_sort == 'popular' %}selected{% endif %}>Most Popular</option>
                        <option value="trending" {% if current_sort == 'trending' %}selected{% endif %}>Trending</option>
                        <option value="featured" {% if current_sort == 'featured' %}selected{% endif %}>Featured First</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary Content Display Area -->
<section class="py-16 bg-gradient-to-b from-white to-gray-50" id="resources-content">
    <!-- Content will be loaded via HTMX -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center py-12">
            <div class="animate-pulse">
                <div class="w-16 h-16 bg-harrier-red bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-spinner fa-spin text-harrier-red text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-harrier-dark font-montserrat mb-2">Loading Resources...</h3>
                <p class="text-gray-600 font-raleway">Discovering the latest automotive insights for you.</p>
            </div>
        </div>
    </div>
</section>

<!-- Featured Posts Section -->
{% if featured_posts %}
<section class="py-16 bg-gradient-to-r from-black via-gray-900 to-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12 animate-fade-in-up">
            <h2 class="text-4xl font-montserrat font-bold text-white mb-4">Featured Resources</h2>
            <p class="text-gray-300 font-raleway text-lg">Handpicked content to accelerate your automotive journey</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
            {% for post in featured_posts %}
            <article class="group relative h-96 rounded-2xl overflow-hidden shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2">
                <div class="absolute inset-0">
                    {% if post.featured_image %}
                        <img src="{{ post.featured_image.url }}" alt="{{ post.featured_image_alt|default:post.title }}"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700">
                    {% else %}
                        <div class="w-full h-full bg-gradient-to-br from-harrier-red to-black flex items-center justify-center">
                            <i class="fas fa-{{ post.content_type|default:'newspaper' }} text-white text-6xl opacity-50"></i>
                        </div>
                    {% endif %}
                </div>

                <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>

                <!-- Content Type Badge -->
                <div class="absolute top-4 left-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-harrier-red text-white">
                        <i class="fas fa-{{ post.content_type|default:'newspaper' }} mr-1"></i>
                        {{ post.content_type_display|default:'Article' }}
                    </span>
                </div>

                <!-- Featured Badge -->
                <div class="absolute top-4 right-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
                        <i class="fas fa-star mr-1"></i>Featured
                    </span>
                </div>

                <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                    <div class="flex items-center text-sm mb-3 opacity-90">
                        <i class="fas fa-calendar mr-2"></i>
                        <span>{{ post.published_at|date:"M d, Y" }}</span>
                        <span class="mx-2">•</span>
                        <span>{{ post.estimated_read_time }} min read</span>
                        {% if post.views_count %}
                        <span class="mx-2">•</span>
                        <span>{{ post.views_count }} views</span>
                        {% endif %}
                    </div>

                    <h3 class="text-2xl font-montserrat font-bold mb-3 group-hover:text-harrier-red transition-colors">
                        <a href="{% url 'core:resource_detail' post.slug %}">{{ post.title }}</a>
                    </h3>

                    <p class="text-gray-200 mb-4 font-raleway">{{ post.excerpt|default:post.content|truncatewords:15 }}</p>

                    <a href="{% url 'core:resource_detail' post.slug %}"
                       class="inline-flex items-center text-white font-semibold hover:text-harrier-red transition-colors group">
                        Read More
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                    </a>
                </div>
            </article>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Regular Content Grid -->
<section class="py-16 bg-gradient-to-b from-white to-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="animate-fade-in-up" style="animation-delay: 0.3s;">
            {% if not featured_posts %}
            <h2 class="text-3xl font-montserrat font-bold text-harrier-dark mb-8 text-center">Latest Resources</h2>
            {% else %}
            <h2 class="text-3xl font-montserrat font-bold text-harrier-dark mb-8 text-center">More Resources</h2>
            {% endif %}

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for post in posts %}
                    {% if not post.is_featured %}
                    <article class="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1 overflow-hidden border border-gray-100">
                        <div class="relative h-48 overflow-hidden">
                            {% if post.featured_image %}
                                <img src="{{ post.featured_image.url }}" alt="{{ post.featured_image_alt|default:post.title }}"
                                     class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                            {% else %}
                                <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                    <i class="fas fa-{{ post.content_type|default:'newspaper' }} text-gray-400 text-4xl"></i>
                                </div>
                            {% endif %}

                            <!-- Content Type Badge -->
                            <div class="absolute top-3 left-3">
                                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-semibold bg-harrier-red text-white">
                                    <i class="fas fa-{{ post.content_type|default:'newspaper' }} mr-1"></i>
                                    {{ post.content_type_display|default:'Article' }}
                                </span>
                            </div>

                            <!-- Reading Time -->
                            <div class="absolute top-3 right-3">
                                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-semibold bg-black bg-opacity-70 text-white">
                                    <i class="fas fa-clock mr-1"></i>
                                    {{ post.estimated_read_time }} min
                                </span>
                            </div>
                        </div>

                        <div class="p-6">
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <i class="fas fa-calendar mr-2"></i>
                                <span>{{ post.published_at|date:"M d, Y" }}</span>
                                {% if post.views_count %}
                                <span class="mx-2">•</span>
                                <span>{{ post.views_count }} views</span>
                                {% endif %}
                            </div>

                            <h3 class="text-xl font-montserrat font-bold mb-3 group-hover:text-harrier-red transition-colors">
                                <a href="{% url 'core:resource_detail' post.slug %}">{{ post.title }}</a>
                            </h3>

                            <p class="text-gray-600 mb-4 font-raleway">{{ post.excerpt|default:post.content|truncatewords:20 }}</p>

                            <!-- Tags -->
                            {% if post.tags.all %}
                            <div class="flex flex-wrap gap-2 mb-4">
                                {% for tag in post.tags.all|slice:":3" %}
                                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700">
                                    {{ tag.name }}
                                </span>
                                {% endfor %}
                            </div>
                            {% endif %}

                            <!-- Content Type Specific Features -->
                            {% if post.content_type == 'opinion' %}
                                <!-- Opinion Features -->
                                <div class="flex items-center gap-4 mb-4 text-sm text-gray-600">
                                    {% if post.poll %}
                                    <div class="flex items-center">
                                        <i class="fas fa-poll text-blue-500 mr-1"></i>
                                        <span>Interactive Poll</span>
                                    </div>
                                    {% endif %}
                                    {% if post.opinion_reviews.count > 0 %}
                                    <div class="flex items-center">
                                        <i class="fas fa-star text-yellow-500 mr-1"></i>
                                        <span>{{ post.opinion_reviews.count }} Review{{ post.opinion_reviews.count|pluralize }}</span>
                                    </div>
                                    {% endif %}
                                </div>
                            {% elif post.content_type == 'guide' %}
                                <!-- Guide Features -->
                                <div class="flex items-center gap-4 mb-4 text-sm text-gray-600">
                                    {% if post.has_pdf %}
                                    <div class="flex items-center">
                                        <i class="fas fa-file-pdf text-red-500 mr-1"></i>
                                        <span>PDF Available ({{ post.pdf_file_size_formatted }})</span>
                                    </div>
                                    {% endif %}
                                    {% if post.pdf_download_count > 0 %}
                                    <div class="flex items-center">
                                        <i class="fas fa-download text-green-500 mr-1"></i>
                                        <span>{{ post.pdf_download_count }} Download{{ post.pdf_download_count|pluralize }}</span>
                                    </div>
                                    {% endif %}
                                </div>
                            {% elif post.content_type == 'infographic' %}
                                <!-- Infographic Features -->
                                <div class="flex items-center gap-4 mb-4 text-sm text-gray-600">
                                    {% if post.has_chart_data %}
                                    <div class="flex items-center">
                                        <i class="fas fa-chart-{{ post.chart_type|default:'bar' }} text-purple-500 mr-1"></i>
                                        <span>Interactive {{ post.chart_type|title|default:'Chart' }}</span>
                                    </div>
                                    {% endif %}
                                    <div class="flex items-center">
                                        <i class="fas fa-eye text-blue-500 mr-1"></i>
                                        <span>Data Visualization</span>
                                    </div>
                                </div>
                            {% endif %}

                            <div class="flex items-center justify-between">
                                <a href="{% url 'core:resource_detail' post.slug %}"
                                   class="inline-flex items-center text-harrier-red font-semibold hover:text-harrier-dark transition-colors group">
                                    {% if post.content_type == 'opinion' %}
                                        View & Vote
                                    {% elif post.content_type == 'guide' %}
                                        Read Guide
                                    {% elif post.content_type == 'infographic' %}
                                        Explore Data
                                    {% else %}
                                        Read More
                                    {% endif %}
                                    <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                                </a>

                                <!-- Quick Action Buttons -->
                                {% if post.content_type == 'guide' and post.has_pdf %}
                                <a href="{% url 'core:guide_pdf_download' post.id %}"
                                   class="inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 bg-red-50 rounded-full hover:bg-red-100 transition-colors"
                                   title="Download PDF">
                                    <i class="fas fa-download mr-1"></i>
                                    PDF
                                </a>
                                {% elif post.content_type == 'infographic' and post.has_chart_data %}
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium text-purple-600 bg-purple-50 rounded-full"
                                      title="Interactive Chart Available">
                                    <i class="fas fa-chart-line mr-1"></i>
                                    Interactive
                                </span>
                                {% elif post.content_type == 'opinion' and post.poll %}
                                <span class="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-full"
                                      title="Poll Available">
                                    <i class="fas fa-poll mr-1"></i>
                                    Poll
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </article>
                    {% endif %}
                {% empty %}
                    <div class="col-span-full text-center py-12">
                        <div class="w-24 h-24 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-search text-gray-400 text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-montserrat font-semibold text-gray-700 mb-2">No Resources Found</h3>
                        <p class="text-gray-500 font-raleway">Try adjusting your search criteria or browse all resources.</p>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Features Call-to-Action Section -->
<section class="py-20 bg-gradient-to-r from-harrier-red to-harrier-dark relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white font-montserrat mb-6">
            Experience Our Enhanced Features
        </h2>
        <p class="text-xl text-white/90 font-raleway max-w-3xl mx-auto mb-12">
            Discover interactive polls, downloadable guides, data visualizations, and expert insights designed to enhance your automotive knowledge
        </p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <!-- Interactive Polls CTA -->
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-poll text-2xl text-white"></i>
                </div>
                <h3 class="text-lg font-bold text-white font-montserrat mb-2">Vote in Polls</h3>
                <p class="text-white/80 font-raleway text-sm">
                    Share your opinions and see real-time results from the automotive community
                </p>
            </div>

            <!-- PDF Guides CTA -->
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-file-pdf text-2xl text-white"></i>
                </div>
                <h3 class="text-lg font-bold text-white font-montserrat mb-2">Download Guides</h3>
                <p class="text-white/80 font-raleway text-sm">
                    Access comprehensive PDF guides for offline reading and reference
                </p>
            </div>

            <!-- Data Visualization CTA -->
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-chart-line text-2xl text-white"></i>
                </div>
                <h3 class="text-lg font-bold text-white font-montserrat mb-2">Explore Data</h3>
                <p class="text-white/80 font-raleway text-sm">
                    Interact with charts and visualizations to understand market trends
                </p>
            </div>
        </div>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="?content_type=opinion"
               class="bg-white text-harrier-red px-8 py-4 rounded-xl font-bold font-montserrat hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 hover:shadow-xl">
                <i class="fas fa-poll mr-2"></i>
                Explore Polls & Opinions
            </a>
            <a href="?content_type=guide"
               class="bg-transparent border-2 border-white text-white px-8 py-4 rounded-xl font-bold font-montserrat hover:bg-white hover:text-harrier-red transition-all duration-300 transform hover:scale-105">
                <i class="fas fa-download mr-2"></i>
                Browse PDF Guides
            </a>
        </div>
    </div>
</section>
{% endblock %}

<!-- Enhanced JavaScript for Modern Functionality -->
{% block extra_js %}
{{ block.super }}
<script>
// Enhanced Resources Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the page
    initializeResourcesPage();

    // Load default content
    loadDefaultContent();

    // Setup filter interactions
    setupFilterTabs();

    // Setup search enhancements
    setupSearchEnhancements();

    // Setup content type features
    setupContentTypeFeatures();

    // Setup infinite scroll (optional)
    setupInfiniteScroll();

    // Initialize feature showcase animations
    initializeFeatureShowcase();
});

function initializeResourcesPage() {
    // Add staggered animations to filter tabs
    const filterTabs = document.querySelectorAll('.filter-tab');
    filterTabs.forEach((tab, index) => {
        tab.style.animationDelay = `${0.1 + (index * 0.05)}s`;
        tab.classList.add('animate-fade-in-up');
    });

    // Add hover effects to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
}

function loadDefaultContent() {
    // Load initial content
    const defaultTab = document.querySelector('.filter-tab.active');
    if (defaultTab) {
        // Trigger HTMX request for default content
        setTimeout(() => {
            htmx.trigger(defaultTab, 'click');
        }, 500);
    }
}

function setupFilterTabs() {
    const filterTabs = document.querySelectorAll('.filter-tab');

    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            filterTabs.forEach(t => {
                t.classList.remove('active');
                t.style.transform = '';
            });

            // Add active class to clicked tab
            this.classList.add('active');
            this.style.transform = 'translateY(-2px)';

            // Add loading state to content area
            showContentLoading();

            // Update URL without page reload
            updateURLState(this);
        });
    });
}

function setupSearchEnhancements() {
    const searchInput = document.querySelector('input[name="search"]');
    const searchButton = document.querySelector('.search-button');

    if (searchInput) {
        // Add search suggestions (future enhancement)
        searchInput.addEventListener('focus', function() {
            this.style.transform = 'translateY(-2px)';
        });

        searchInput.addEventListener('blur', function() {
            this.style.transform = '';
        });

        // Enhanced search feedback
        searchInput.addEventListener('input', function() {
            const searchText = searchButton.querySelector('.search-text');
            if (this.value.length > 0) {
                searchText.textContent = 'Search';
                searchButton.style.background = 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)';
            } else {
                searchText.textContent = 'Browse';
                searchButton.style.background = 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)';
            }
        });
    }
}

function setupInfiniteScroll() {
    // Infinite scroll implementation (optional)
    let isLoading = false;

    window.addEventListener('scroll', function() {
        if (isLoading) return;

        const scrollPosition = window.innerHeight + window.scrollY;
        const documentHeight = document.documentElement.offsetHeight;

        if (scrollPosition >= documentHeight - 1000) {
            // Load more content
            loadMoreContent();
        }
    });
}

function showContentLoading() {
    const contentArea = document.getElementById('resources-content');
    if (contentArea) {
        contentArea.innerHTML = `
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center py-12">
                    <div class="animate-pulse">
                        <div class="w-16 h-16 bg-harrier-red bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-spinner fa-spin text-harrier-red text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-harrier-dark font-montserrat mb-2">Loading Content...</h3>
                        <p class="text-gray-600 font-raleway">Fetching the latest resources for you.</p>
                    </div>
                </div>
            </div>
        `;
    }
}

function updateURLState(tab) {
    const filter = tab.dataset.filter;
    const url = new URL(window.location);

    if (filter === 'all') {
        url.searchParams.delete('content_type');
    } else {
        url.searchParams.set('content_type', filter);
    }

    // Update URL without page reload
    window.history.pushState({}, '', url);
}

function loadMoreContent() {
    // Implementation for infinite scroll
    console.log('Loading more content...');
}

// HTMX Event Handlers
document.addEventListener('htmx:beforeRequest', function(event) {
    // Show loading state for HTMX requests
    if (event.target.classList.contains('filter-tab') ||
        event.target.closest('form')) {
        showContentLoading();
    }
});

document.addEventListener('htmx:afterRequest', function(event) {
    // Handle successful content load
    if (event.detail.successful) {
        // Add entrance animations to new content
        const newContent = document.getElementById('resources-content');
        if (newContent) {
            newContent.style.opacity = '0';
            newContent.style.transform = 'translateY(20px)';

            setTimeout(() => {
                newContent.style.transition = 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)';
                newContent.style.opacity = '1';
                newContent.style.transform = 'translateY(0)';
            }, 100);
        }

        // Update statistics if available
        updateContentStatistics();
    }
});

document.addEventListener('htmx:responseError', function(event) {
    // Handle errors gracefully
    const contentArea = document.getElementById('resources-content');
    if (contentArea) {
        contentArea.innerHTML = `
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-red-600 font-montserrat mb-2">Loading Failed</h3>
                    <p class="text-gray-600 font-raleway mb-4">We encountered an issue loading the content.</p>
                    <button class="inline-flex items-center px-4 py-2 bg-harrier-red text-white rounded-lg font-semibold hover:bg-red-700 transition-colors"
                            onclick="location.reload()">
                        <i class="fas fa-refresh mr-2"></i>Try Again
                    </button>
                </div>
            </div>
        `;
    }
});

function updateContentStatistics() {
    // Update statistics based on current filter
    // This would be implemented with real data
    console.log('Updating content statistics...');
}

// Enhanced keyboard navigation
document.addEventListener('keydown', function(event) {
    // Implement keyboard shortcuts for better UX
    if (event.ctrlKey || event.metaKey) {
        switch(event.key) {
            case 'k':
                event.preventDefault();
                document.querySelector('input[name="search"]').focus();
                break;
            case '1':
                event.preventDefault();
                document.querySelector('[data-filter="all"]').click();
                break;
            case '2':
                event.preventDefault();
                document.querySelector('[data-filter="article"]').click();
                break;
            // Add more shortcuts as needed
        }
    }
});

// Enhanced Content Type Features
function setupContentTypeFeatures() {
    // Add hover effects to content type cards
    const resourceCards = document.querySelectorAll('.resource-card');
    resourceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const contentType = this.dataset.contentType;
            if (contentType) {
                this.classList.add(`hover-${contentType}`);
            }
        });

        card.addEventListener('mouseleave', function() {
            const contentType = this.dataset.contentType;
            if (contentType) {
                this.classList.remove(`hover-${contentType}`);
            }
        });
    });

    // Add click tracking for feature badges
    const featureBadges = document.querySelectorAll('.feature-badge, .quick-action-btn');
    featureBadges.forEach(badge => {
        badge.addEventListener('click', function() {
            const feature = this.dataset.feature || this.textContent.trim();
            console.log(`Feature interaction: ${feature}`);

            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

function initializeFeatureShowcase() {
    // Animate feature showcase cards on scroll
    const showcaseCards = document.querySelectorAll('.feature-showcase-card');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    showcaseCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
}

// Enhanced filter functionality
function enhanceFilterTabs() {
    const filterTabs = document.querySelectorAll('.filter-tab');

    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            filterTabs.forEach(t => t.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Add loading state
            const contentArea = document.getElementById('resources-content');
            if (contentArea) {
                contentArea.style.opacity = '0.7';
                contentArea.style.transition = 'opacity 0.3s ease';

                // Remove loading state after HTMX completes
                setTimeout(() => {
                    contentArea.style.opacity = '1';
                }, 500);
            }
        });
    });
}

// Content type specific animations
function addContentTypeAnimations() {
    const contentCards = document.querySelectorAll('[data-content-type]');

    contentCards.forEach(card => {
        const contentType = card.dataset.contentType;

        // Add specific animations based on content type
        switch(contentType) {
            case 'opinion':
                card.addEventListener('mouseenter', () => {
                    const pollIcon = card.querySelector('.fa-poll');
                    if (pollIcon) {
                        pollIcon.style.animation = 'pulse 1s infinite';
                    }
                });
                break;

            case 'guide':
                card.addEventListener('mouseenter', () => {
                    const pdfIcon = card.querySelector('.fa-file-pdf');
                    if (pdfIcon) {
                        pdfIcon.style.animation = 'bounce 1s infinite';
                    }
                });
                break;

            case 'infographic':
                card.addEventListener('mouseenter', () => {
                    const chartIcon = card.querySelector('.fa-chart-line');
                    if (chartIcon) {
                        chartIcon.style.animation = 'fadeIn 0.5s ease-in-out';
                    }
                });
                break;
        }
    });
}

// Performance monitoring
let pageLoadStart = performance.now();
window.addEventListener('load', function() {
    const loadTime = performance.now() - pageLoadStart;
    console.log(`Enhanced Resources page loaded in ${loadTime.toFixed(2)}ms`);

    // Initialize enhanced features after load
    enhanceFilterTabs();
    addContentTypeAnimations();
});
</script>
{% endblock %}

