{% load static %}

<!-- Guide PDF Section Component -->
{% if post.content_type == 'guide' %}
<div class="guide-pdf-section mb-8 animate-fade-in-up" style="animation-delay: 0.4s;">
    {% if post.has_pdf %}
    <!-- PDF Available -->
    <div class="glassmorphism-card p-6">
        <div class="pdf-header flex items-center justify-between mb-4">
            <div>
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat mb-1">
                    <i class="fas fa-file-pdf text-red-600 mr-2"></i>
                    Complete Guide (PDF)
                </h3>
                <p class="text-sm text-gray-600 font-raleway">
                    Download the full guide as a PDF document
                </p>
            </div>
            <div class="pdf-badge bg-red-100 text-red-800 px-3 py-1 rounded-full text-xs font-medium">
                {{ post.pdf_file_size_formatted }}
            </div>
        </div>

        <div class="pdf-stats flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-4">
                <div class="stat-item">
                    <span class="text-xs text-gray-500 font-raleway">Downloads</span>
                    <div class="text-sm font-bold text-harrier-dark">{{ post.pdf_download_count }}</div>
                </div>
                <div class="stat-item">
                    <span class="text-xs text-gray-500 font-raleway">Format</span>
                    <div class="text-sm font-bold text-harrier-dark">PDF</div>
                </div>
            </div>
            <div class="flex items-center text-xs text-gray-500">
                <i class="fas fa-shield-alt mr-1"></i>
                Secure Download
            </div>
        </div>

        <div class="pdf-actions flex flex-col sm:flex-row gap-3">
            <!-- Quick View Button -->
            <button class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-lg font-medium font-raleway hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                    onclick="openPdfViewer({{ post.id }})">
                <i class="fas fa-eye mr-2"></i>
                Quick View
            </button>

            <!-- Download Button -->
            <a href="{% url 'core:guide_pdf_download' post.id %}" 
               class="flex-1 bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-3 rounded-lg font-medium font-raleway hover:from-green-700 hover:to-green-800 transition-all duration-300 transform hover:scale-105 hover:shadow-lg text-center">
                <i class="fas fa-download mr-2"></i>
                Download PDF
            </a>

            <!-- Full Info Button -->
            <button class="flex-1 bg-gradient-to-r from-harrier-red to-harrier-dark text-white px-4 py-3 rounded-lg font-medium font-raleway hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                    hx-get="{% url 'core:guide_pdf_info' post.id %}"
                    hx-target="#pdf-info-modal"
                    hx-swap="innerHTML"
                    onclick="showPdfInfoModal()">
                <i class="fas fa-info-circle mr-2"></i>
                More Info
            </button>
        </div>

        <!-- Inline Preview Toggle -->
        <div class="pdf-preview-toggle mt-4">
            <button class="w-full text-center text-sm text-harrier-red hover:text-harrier-dark transition-colors duration-200 py-2 border border-gray-200 rounded-lg hover:bg-gray-50"
                    onclick="toggleInlinePreview({{ post.id }})">
                <i class="fas fa-chevron-down mr-1" id="preview-icon-{{ post.id }}"></i>
                <span id="preview-text-{{ post.id }}">Show PDF Preview</span>
            </button>
        </div>

        <!-- Inline PDF Preview (Hidden by default) -->
        <div id="pdf-preview-{{ post.id }}" class="pdf-preview-container mt-4 hidden">
            <div class="border border-gray-200 rounded-lg overflow-hidden">
                <div class="bg-gray-100 px-4 py-2 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700 font-raleway">PDF Preview</span>
                        <div class="flex items-center space-x-2">
                            <button class="text-xs text-harrier-red hover:text-harrier-dark transition-colors duration-200"
                                    onclick="openPdfFullscreen({{ post.id }})">
                                <i class="fas fa-expand-alt mr-1"></i>
                                Fullscreen
                            </button>
                        </div>
                    </div>
                </div>
                <div class="pdf-embed-container" style="height: 500px;">
                    <iframe id="pdf-iframe-{{ post.id }}"
                            width="100%" 
                            height="100%" 
                            frameborder="0"
                            class="pdf-iframe"
                            data-src="{% url 'core:guide_pdf_viewer' post.id %}#toolbar=0&navpanes=0&scrollbar=0">
                    </iframe>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- No PDF Available -->
    <div class="glassmorphism-card p-6 text-center">
        <div class="no-pdf-content">
            <i class="fas fa-file-pdf text-3xl text-gray-400 mb-3"></i>
            <h3 class="text-lg font-medium text-gray-700 font-montserrat mb-2">PDF Version Coming Soon</h3>
            <p class="text-sm text-gray-600 font-raleway">
                We're working on creating a downloadable PDF version of this guide.
            </p>
        </div>
    </div>
    {% endif %}
</div>

<!-- PDF Info Modal Container -->
<div id="pdf-info-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <!-- Content will be loaded here via HTMX -->
</div>

<script>
function openPdfViewer(postId) {
    const url = `{% url 'core:guide_pdf_viewer' 0 %}`.replace('0', postId);
    window.open(url, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
}

function toggleInlinePreview(postId) {
    const preview = document.getElementById(`pdf-preview-${postId}`);
    const icon = document.getElementById(`preview-icon-${postId}`);
    const text = document.getElementById(`preview-text-${postId}`);
    const iframe = document.getElementById(`pdf-iframe-${postId}`);
    
    if (preview.classList.contains('hidden')) {
        preview.classList.remove('hidden');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
        text.textContent = 'Hide PDF Preview';
        
        // Load iframe content
        if (iframe && !iframe.src) {
            iframe.src = iframe.dataset.src;
        }
    } else {
        preview.classList.add('hidden');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
        text.textContent = 'Show PDF Preview';
    }
}

function openPdfFullscreen(postId) {
    const url = `{% url 'core:guide_pdf_viewer' 0 %}`.replace('0', postId);
    window.open(url, '_blank', 'fullscreen=yes');
}

function showPdfInfoModal() {
    const modal = document.getElementById('pdf-info-modal');
    modal.classList.remove('hidden');
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.classList.add('hidden');
        }
    });
}

// Close modal with escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modal = document.getElementById('pdf-info-modal');
        modal.classList.add('hidden');
    }
});
</script>

<style>
.glassmorphism-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.pdf-badge {
    font-family: 'Raleway', sans-serif;
}

.stat-item {
    text-align: center;
}

.pdf-embed-container {
    background: #f8f9fa;
    position: relative;
}

.pdf-iframe {
    border: none;
    background: white;
}

.pdf-preview-container {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
{% endif %}
