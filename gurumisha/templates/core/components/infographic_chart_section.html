{% load static %}

<!-- Infographic Chart Section Component -->
{% if post.content_type == 'infographic' %}
<div class="infographic-chart-section mb-8 animate-fade-in-up" style="animation-delay: 0.4s;">
    {% if post.has_chart_data %}
    <!-- Chart Available -->
    <div class="glassmorphism-card p-6">
        <div class="chart-header flex items-center justify-between mb-6">
            <div>
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat mb-1">
                    <i class="fas fa-chart-bar text-harrier-red mr-2"></i>
                    Interactive Data Visualization
                </h3>
                <p class="text-sm text-gray-600 font-raleway">
                    Explore the data through interactive charts and graphs
                </p>
            </div>
            <div class="chart-badge bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-medium">
                {{ post.chart_type|title }} Chart
            </div>
        </div>

        <!-- Quick Chart Preview -->
        <div class="chart-preview-mini mb-4">
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200">
                <div class="flex items-center justify-center h-32">
                    <div class="text-center">
                        <i class="fas fa-chart-{{ post.chart_type }} text-4xl text-harrier-red mb-2"></i>
                        <p class="text-sm text-gray-600 font-raleway">{{ post.chart_type|title }} Visualization</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chart Actions -->
        <div class="chart-actions flex flex-col sm:flex-row gap-3 mb-4">
            <!-- View Interactive Chart -->
            <button class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-lg font-medium font-raleway hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                    hx-get="{% url 'core:infographic_chart_preview' post.id %}"
                    hx-target="#chart-preview-modal"
                    hx-swap="innerHTML"
                    onclick="showChartModal()">
                <i class="fas fa-chart-line mr-2"></i>
                View Interactive Chart
            </button>

            <!-- Chart Data API -->
            <button class="flex-1 bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-3 rounded-lg font-medium font-raleway hover:from-green-700 hover:to-green-800 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                    onclick="getChartData({{ post.id }})">
                <i class="fas fa-download mr-2"></i>
                Get Data (JSON)
            </button>

            <!-- Embed Chart -->
            <button class="flex-1 bg-gradient-to-r from-purple-600 to-purple-700 text-white px-4 py-3 rounded-lg font-medium font-raleway hover:from-purple-700 hover:to-purple-800 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                    onclick="showEmbedCode({{ post.id }})">
                <i class="fas fa-code mr-2"></i>
                Embed Chart
            </button>
        </div>
{% if post.content_type == 'infographic' %}
<div class="infographic-chart-section mb-8 animate-fade-in-up" style="animation-delay: 0.4s;">
    {% if post.has_chart_data %}
    <!-- Chart Available -->
    <div class="glassmorphism-card p-6">
        <div class="chart-header flex items-center justify-between mb-6">
            <div>
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat mb-1">
                    <i class="fas fa-chart-bar text-harrier-red mr-2"></i>
                    Interactive Data Visualization
                </h3>
                <p class="text-sm text-gray-600 font-raleway">
                    Explore the data through interactive charts and graphs
                </p>
            </div>
            <div class="chart-badge bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-medium">
                {{ post.chart_type|title }} Chart
            </div>
        </div>

        <!-- Quick Chart Preview -->
        <div class="chart-preview-mini mb-4">
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200">
                <canvas id="mini-chart-{{ post.id }}" 
                        width="300" 
                        height="150"
                        class="w-full max-h-32">
                </canvas>
            </div>
        </div>

        <!-- Chart Actions -->
        <div class="chart-actions flex flex-col sm:flex-row gap-3">
            <!-- View Full Chart Button -->
            <button class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-lg font-medium font-raleway hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                    hx-get="{% url 'core:infographic_chart_preview' post.id %}"
                    hx-target="#chart-modal-content"
                    hx-swap="innerHTML"
                    onclick="showChartModal()">
                <i class="fas fa-expand-alt mr-2"></i>
                View Interactive Chart
            </button>

            <!-- Download Chart Data -->
            <button class="flex-1 bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-3 rounded-lg font-medium font-raleway hover:from-green-700 hover:to-green-800 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                    onclick="downloadChartData({{ post.id }})">
                <i class="fas fa-download mr-2"></i>
                Download Data
            </button>

            <!-- Chart Info -->
            <button class="flex-1 bg-gradient-to-r from-harrier-red to-harrier-dark text-white px-4 py-3 rounded-lg font-medium font-raleway hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                    onclick="showChartInfo({{ post.id }})">
                <i class="fas fa-info-circle mr-2"></i>
                Chart Details
            </button>
        </div>

        <!-- Chart Statistics -->
        <div class="chart-stats mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="stat-card text-center p-3 bg-gray-50 rounded-lg">
                <div class="stat-icon mb-1">
                    <i class="fas fa-chart-line text-lg text-harrier-red"></i>
                </div>
                <div class="stat-value text-sm font-bold text-harrier-dark">{{ post.chart_type|title }}</div>
                <div class="stat-label text-xs text-gray-600">Type</div>
            </div>
            
            <div class="stat-card text-center p-3 bg-gray-50 rounded-lg">
                <div class="stat-icon mb-1">
                    <i class="fas fa-mouse-pointer text-lg text-harrier-red"></i>
                </div>
                <div class="stat-value text-sm font-bold text-harrier-dark">Interactive</div>
                <div class="stat-label text-xs text-gray-600">Mode</div>
            </div>
            
            <div class="stat-card text-center p-3 bg-gray-50 rounded-lg">
                <div class="stat-icon mb-1">
                    <i class="fas fa-mobile-alt text-lg text-harrier-red"></i>
                </div>
                <div class="stat-value text-sm font-bold text-harrier-dark">Responsive</div>
                <div class="stat-label text-xs text-gray-600">Design</div>
            </div>
            
            <div class="stat-card text-center p-3 bg-gray-50 rounded-lg">
                <div class="stat-icon mb-1">
                    <i class="fas fa-download text-lg text-harrier-red"></i>
                </div>
                <div class="stat-value text-sm font-bold text-harrier-dark">Exportable</div>
                <div class="stat-label text-xs text-gray-600">Format</div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- No Chart Available -->
    <div class="glassmorphism-card p-6 text-center">
        <div class="no-chart-content">
            <i class="fas fa-chart-bar text-3xl text-gray-400 mb-3"></i>
            <h3 class="text-lg font-medium text-gray-700 font-montserrat mb-2">Interactive Chart Coming Soon</h3>
            <p class="text-sm text-gray-600 font-raleway">
                We're working on creating interactive data visualizations for this infographic.
            </p>
        </div>
    </div>
    {% endif %}
</div>

<!-- Chart Modal Container -->
<div id="chart-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
            <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Interactive Chart Viewer</h3>
            <button onclick="closeChartModal()" class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="chart-modal-content" class="p-6">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize mini chart preview
document.addEventListener('DOMContentLoaded', function() {
    {% if post.has_chart_data %}
    initializeMiniChart({{ post.id }});
    {% endif %}
});

function initializeMiniChart(postId) {
    // Fetch chart data
    fetch(`{% url 'core:infographic_chart_data' 0 %}`.replace('0', postId))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const ctx = document.getElementById(`mini-chart-${postId}`).getContext('2d');
                
                // Simplified config for mini chart
                const miniConfig = {
                    ...data.chart_config,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            title: {
                                display: false
                            }
                        },
                        scales: data.chart_config.type !== 'pie' && data.chart_config.type !== 'doughnut' ? {
                            x: {
                                display: false
                            },
                            y: {
                                display: false
                            }
                        } : {}
                    }
                };
                
                new Chart(ctx, miniConfig);
            }
        })
        .catch(error => {
            console.error('Error loading mini chart:', error);
        });
}

function showChartModal() {
    const modal = document.getElementById('chart-modal');
    modal.classList.remove('hidden');
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeChartModal();
        }
    });
}

function closeChartModal() {
    const modal = document.getElementById('chart-modal');
    modal.classList.add('hidden');
}

function downloadChartData(postId) {
    // Fetch chart data and download as JSON
    fetch(`{% url 'core:infographic_chart_data' 0 %}`.replace('0', postId))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const dataStr = JSON.stringify(data.chart_config.data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `{{ post.title|slugify }}-chart-data.json`;
                link.click();
                URL.revokeObjectURL(url);
            }
        })
        .catch(error => {
            console.error('Error downloading chart data:', error);
        });
}

function showChartInfo(postId) {
    // Show chart information
    alert('Chart Type: {{ post.chart_type|title }}\nInteractive: Yes\nExportable: Yes\nResponsive: Yes');
}

// Close modal with escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeChartModal();
    }
});
</script>

<style>
.glassmorphism-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-badge {
    font-family: 'Raleway', sans-serif;
}

.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chart-preview-mini canvas {
    border-radius: 6px;
}

#chart-modal {
    backdrop-filter: blur(4px);
}
</style>
{% endif %}
